# CiteAI - AI-Powered Citation Generator

Transform your essays into perfectly formatted works cited pages in seconds. CiteAI automatically detects sources and generates accurate citations in any format using advanced AI technology.

## 🚀 Features

- **AI-Powered Source Detection**: Automatically identifies sources that need citations using Perplexity API
- **Multiple Citation Formats**: Support for MLA, APA, Chicago, and Harvard formats
- **PDF Generation**: Export citations as professionally formatted PDFs (Plus/Pro plans)
- **PDF Document Processing**: Upload PDF documents for automatic text extraction (Plus/Pro plans)
- **User Authentication**: Secure user accounts with Supabase Auth
- **Subscription Plans**: Free, Plus, and Pro tiers with different limits and features
- **Real-time Processing**: Fast citation generation with progress tracking
- **Drag & Drop Interface**: Intuitive file upload for Pro users
- **Citation Management**: Store, organize, and manage citations across documents

## 🔧 Recent Updates

### 2025-09-26: PDF Modal Layer & Permission Fixes
- **🎨 PDF Export Modal Z-Index Fixed**: Resolved layer conflict where PDF export modal was hidden behind citation modal
- **🖱️ PDF Export Modal Interaction Enhanced**: Improved z-index from 9999 to 99999 with inline style backup for maximum compatibility
- **📤 PDF Import Permission Corrected**: Fixed frontend permission check to allow Plus users (was Pro-only) for PDF document import
- **✅ Plus User PDF Access Restored**: Plus users can now properly access both PDF import and export features as intended
- **🧪 Comprehensive Testing Added**: Created specialized test suites for PDF modal layering and permission validation
- **📋 UI/UX Improvements**: Enhanced modal interaction with proper event handling and visual hierarchy
- **🔍 Permission Logic Unified**: Aligned frontend and backend permission checks for consistent Plus/Pro user access

### 2025-09-24: PDF Features & Citation Workflow Complete Fix
- **📄 PDF Import Permission Fixed**: Corrected PDF processing to allow both Plus and Pro users (was Pro-only)
- **🎨 PDF Export UI Optimized**: Replaced ugly popup window with elegant in-page modal for better user experience
- **🔧 PDF Export Modal Layer Fixed**: Fixed z-index conflict ensuring PDF export modal displays above citation modals
- **🖱️ PDF Export Modal Interaction Fixed**: Resolved event propagation issues, added keyboard support (ESC), and scroll control
- **🔐 PDF Import Authentication Enhanced**: Improved session validation, refresh mechanism, and comprehensive error handling
- **📊 PDF Import Plan Validation Enhanced**: Added multi-field support (plan_type/subscription_tier), case-insensitive matching, and detailed debugging
- **🔗 Citation API 404 Error Fixed**: Resolved critical issue where Pro/Plus users encountered 404 errors when retrieving citations
- **🎨 Citation Formatting 500 Error Fixed**: Resolved issue where citation formatting failed with "Missing required fields" error
- **🔐 Authentication Client Updated**: Fixed `/api/citations/get` endpoint to use authenticated Supabase client, resolving RLS policy conflicts
- **⚙️ Lambda Parameter Validation Fixed**: Added temporary workaround for format_only mode parameter validation in citation formatting
- **✅ Complete PDF & Citation Workflow**: Full pipelines now working for Plus/Pro users with optimized UI/UX, robust authentication, and seamless interaction
- **🧪 Comprehensive Testing**: Added specialized test suites validating PDF and citation functionality across user tiers
- **📊 End-to-End Validation**: Verified complete workflows with successful processing, formatting, authentication, and user interaction
- **🔍 Root Cause Analysis**: Fixed permission logic, authentication context, UI layer conflicts, event handling, and user experience issues
- **📋 Enhanced Monitoring**: Added detailed logging and improved error handling across all features with comprehensive debugging support and plan validation

### 2025-09-22: Critical API Issues Fixed for Plus/Pro Users
- **🚨 Database Schema Mismatch Resolved**: Fixed API queries using wrong table names (`profiles` → `users`) and field names (`subscription_plan` → `plan_type`)
- **🔐 Authentication Client Fixed**: Corrected API routes to use authenticated Supabase clients, resolving RLS policy conflicts
- **✅ Plus/Pro User Access Restored**: Citation storage, PDF export, and PDF processing now working correctly for paid users
- **🧪 Comprehensive Testing**: Added automated test suites validating all user tiers (Free/Plus/Pro) and their respective permissions
- **📊 Lambda Function Connectivity**: Verified all AWS Lambda functions are accessible and responding correctly
- **🔍 Root Cause Analysis**: Identified authentication and database query issues causing 404/403 errors
- **📋 API Validation**: All endpoints now properly validate user permissions and return appropriate responses

### Previous Updates: PDF Functionality & Security Enhancements
- **PDF Processing Fixed**: Resolved PyPDF2 dependency issues with Lambda Layer v7
- **Plus/Pro Citation Workflow**: Fixed citation button visibility and workflow for paid plans
- **Enhanced Security**: Comprehensive input validation, rate limiting, and error handling
- **File Upload Security**: Multi-layer validation for PDF uploads with MIME type checking
- **Rate Limiting**: Implemented per-user rate limits (10 PDF ops/min, 50 citations/15min)
- **Authentication Improvements**: Enhanced API endpoint security with proper token validation
- **Error Handling**: Improved error messages while preventing information leakage
- **Code Quality**: Added comprehensive commenting and security best practices

### 2025-09-16: Major Architecture Upgrade & New Features
- **Migrated to AWS Amplify**: Complete infrastructure overhaul using AWS Amplify Gen 2
- **Lambda Functions**: Serverless citation processing with Python-based Lambda functions
- **S3 Integration**: PDF storage and generation with secure presigned URLs
- **PDF Export Feature**: Integrated PDF generation for Plus and Pro users with S3 download links
- **PDF Document Processing**: New Lambda function for Pro users to upload and extract text from PDFs
- **Enhanced Frontend**: Updated document editor with drag-and-drop PDF upload interface
- **Comprehensive Testing**: Added test suites for PDF processing and export functionality
- **Enhanced Performance**: Optimized database queries and caching mechanisms
- **Improved Scalability**: Auto-scaling Lambda functions and optimized resource allocation

## 📁 Project Structure

### Root Directory
```
CiteAI/
├── amplify/                    # AWS Amplify configuration
│   ├── backend.ts             # Amplify backend definition
│   └── functions/             # Lambda functions
│       ├── call-function/     # Main citation processing Lambda
│       ├── pdfGenerator/      # PDF generation Lambda
│       └── pdfProcessor/      # PDF text extraction Lambda
├── src/                       # Next.js application source
│   ├── app/                   # Next.js 15 App Router
│   │   ├── api/              # API routes
│   │   │   ├── cite/         # Citation generation endpoint
│   │   │   ├── citations/    # Citation management endpoints
│   │   │   ├── documents/    # Document processing endpoints
│   │   │   └── stripe/       # Payment processing
│   │   ├── dashboard/        # User dashboard
│   │   ├── document/[id]/    # Document editor with PDF support
│   │   └── get-started/      # Onboarding page
│   ├── components/           # React components
│   │   ├── ui/              # Reusable UI components (Radix UI)
│   │   ├── CitationActionDialog.tsx # Citation management
│   │   └── MicrotransactionPurchase.tsx # Payment components
│   ├── contexts/            # React contexts
│   │   ├── AuthContext.tsx  # Supabase authentication
│   │   └── PlanContext.tsx  # Subscription management
│   └── lib/                 # Utility libraries
│       ├── supabase.ts      # Supabase client configuration
│       ├── stripe.ts        # Stripe integration
│       ├── rate-limiter.ts  # API rate limiting system
│       └── utils.ts         # Helper functions
├── supabase/               # Database configuration
│   ├── config.toml         # Supabase configuration
│   └── migrations/         # Database schema migrations
├── python-dependency_layer/ # Lambda layer dependencies
├── amplify_outputs.json    # Amplify deployment outputs
└── package.json           # Node.js dependencies
```

## 🛠️ Technology Stack

### Backend & Infrastructure
- **AWS Amplify Gen 2**: Full-stack development platform
- **AWS Lambda**: Serverless Python functions for citation processing
- **Python 3.12**: Core citation engine with advanced NLP
- **AWS S3**: File storage for PDFs and generated documents
- **Lambda Layers**: Shared dependencies (NLTK, citeproc-py, WeasyPrint, PyPDF2)

### Citation Processing
- **NLTK**: Natural language processing and tokenization
- **BeautifulSoup**: Web scraping and HTML parsing
- **citeproc-py**: Citation Style Language (CSL) formatting
- **WeasyPrint**: Professional PDF generation
- **Perplexity API**: AI-powered source detection
- **Requests**: HTTP client for external API calls

### Frontend
- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first styling framework
- **Radix UI**: Accessible component primitives
- **Lucide React**: Modern icon library
- **React Hook Form**: Form state management
- **Sonner**: Toast notifications

### Database & Authentication
- **Supabase**: PostgreSQL database with real-time features
- **Row Level Security (RLS)**: Data access control
- **Supabase Auth**: User authentication and session management
- **Database Functions**: Optimized PostgreSQL stored procedures

### Payment & Subscriptions
- **Stripe**: Payment processing and subscription management
- **Stripe Webhooks**: Real-time payment event handling
- **Microtransactions**: Pay-per-citation system

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- AWS Account (for Amplify deployment)
- Supabase account
- Stripe account
- Python 3.12+ (for PDF processing features)

### 1. Clone the Repository
```bash
git clone https://github.com/CiteAI-LLC/CiteAI.git
cd CiteAI
```

### 2. Install Dependencies
```bash
# Install Node.js dependencies
npm install

# Install Python dependencies for PDF processing (development only)
pip install PyPDF2 reportlab

# Note: Production Python dependencies are managed via Lambda layers
```

### 3. Environment Setup
Create `.env.local` in the root directory:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Stripe Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_webhook_secret

# API Keys (configured in Lambda environment)
PERPLEXITY_API_KEY=your_perplexity_key
SEMANTIC_SCHOLAR_API_KEY=your_semantic_scholar_key
```

### 4. Database Setup
1. Create a Supabase project
2. Run the migrations in `supabase/migrations/` in your Supabase SQL editor
3. Row Level Security (RLS) policies are automatically applied

### 5. AWS Amplify Setup
```bash
# Install Amplify CLI
npm install -g @aws-amplify/cli

# Configure AWS credentials
amplify configure

# Deploy to AWS (creates Lambda functions, S3 buckets, etc.)
npx amplify sandbox
```

### 6. Start Development
```bash
# Start the Next.js development server
npm run dev

# Lambda functions are automatically deployed and accessible
```

## 📊 Database Schema

### Core Tables
- **users**: User accounts, subscription tiers, and citation quotas
- **documents**: User documents with content and metadata
- **citations**: Generated citations with CSL data and source information
- **user_subscriptions**: Stripe subscription management
- **api_cache**: Cached API responses for performance optimization
- **api_usage**: Usage tracking and rate limiting
- **citation_formats**: Formatted citation outputs per document

### Advanced Features
- **Row Level Security (RLS)**: Granular data access control
- **Optimized Indexes**: High-performance queries for large datasets
- **Database Functions**: PostgreSQL stored procedures for complex operations
- **Real-time Subscriptions**: Live updates via Supabase realtime
- **Automatic Cleanup**: Scheduled tasks for cache and session management

## 🔧 API Architecture

### Citation Processing
- `POST /api/cite`: Main citation generation endpoint
- **Lambda Integration**: Serverless processing with AWS Lambda
- **Perplexity API**: AI-powered source detection and analysis
- **CSL Processing**: Citation Style Language formatting
- **PDF Generation**: Professional document export

### Document Management
- `POST /api/documents/process-pdf`: PDF text extraction (Pro users)
- **S3 Storage**: Secure file storage with presigned URLs
- **Text Processing**: Advanced NLP for content analysis

### Citation Management
- `POST /api/citations/store`: Store citations in database
- `GET /api/citations/get`: Retrieve document citations
- `POST /api/citations/format`: Format citations for display
- `POST /api/citations/export-pdf`: Export citations as PDF

### Payment Processing
- `POST /api/stripe/create-checkout-session`: Subscription management
- `POST /api/stripe/create-microtransaction-session`: Pay-per-citation
- `POST /api/stripe/webhook`: Real-time payment event handling
- **Quota Management**: Automatic citation limit enforcement

## 🎯 Usage

### For All Users
1. **Sign up** for a free account with email or OAuth
2. **Create a document** in the intuitive editor
3. **Write or paste** your essay content (up to word limits)
4. **Generate citations** with AI-powered source detection
5. **Copy formatted citations** with proper hanging indents

### For Plus Users
- **Export citations as PDF** with professional formatting
- **PDF document upload** with automatic text extraction
- **Higher word limits** (500 words vs 150 for free)
- **More daily citations** (20 vs 3 for free)
- **Citation management** across multiple documents

### For Pro Users
- **Unlimited citations** with no daily limits
- **PDF document upload** with automatic text extraction
- **Drag & drop interface** for seamless file processing
- **Priority processing** and advanced features

## 📈 Subscription Plans

| Feature | Free | Plus | Pro |
|---------|------|------|-----|
| Daily Citations | 3 | 20 | Unlimited |
| Word Limit | 150 | 500 | 500 |
| PDF Export | ❌ | ✅ | ✅ |
| PDF Upload | ❌ | ✅ | ✅ |
| Citation Storage | ❌ | ✅ | ✅ |
| Priority Support | ❌ | ❌ | ✅ |

### Microtransactions
- **Pay-per-citation** option for Free and Plus users
- **Flexible pricing** when you need extra citations
- **No subscription required** for occasional use

## 🔧 Troubleshooting

### Common Production Issues

#### API 404/403 Errors for Plus/Pro Users
**Status**: ✅ **RESOLVED** - Fixed in 2025-09-22 update

Previous issues with Plus/Pro users experiencing API errors have been resolved:
- Fixed database table/field name mismatches
- Corrected authentication client usage
- Restored proper user permission validation

If you still encounter 404 or 403 errors:

1. **Clear Build Cache**:
   ```bash
   rm -rf .next
   rm -rf node_modules/.cache
   npm run build
   ```

2. **Verify Lambda Functions**:
   - Check `amplify_outputs.json` contains correct Lambda URLs
   - Test Lambda functions directly using the test scripts in `/test/`

3. **Check Environment Variables**:
   - Ensure all required environment variables are set in production
   - Verify Supabase and AWS credentials are correct

4. **Run API Tests**:
   ```bash
   node comprehensive_test.js
   ```

#### Next.js Module Resolution Errors
If you see "Cannot find module" errors:

1. **Clean Installation**:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **Rebuild Application**:
   ```bash
   npm run build
   npm run dev
   ```

#### Testing API Endpoints
Use the provided test scripts to verify functionality:

```bash
# Test all API endpoints
node test/api_functionality_test.js

# Test Lambda connectivity
node test/lambda_connectivity_test.js

# End-to-end workflow test
node test/end_to_end_test.js
```

### Production Deployment Checklist

- [ ] Clear build cache before deployment
- [ ] Verify all environment variables are set
- [ ] Test Lambda function connectivity
- [ ] Run API functionality tests
- [ ] Check Supabase database connectivity
- [ ] Verify AWS S3 bucket permissions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support, please open an issue on GitHub or contact the development team.

---

**Built with ❤️ using Next.js, Supabase, and Python**
