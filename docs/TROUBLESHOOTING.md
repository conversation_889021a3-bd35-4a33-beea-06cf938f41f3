# CiteAI Troubleshooting Guide

This document provides solutions for common issues encountered when developing or deploying CiteAI.

## 🚨 Critical Production Issues (Resolved 2025-09-22)

### Issue: API 404/403 Errors for Plus/Pro Users

**Symptoms:**
- Plus/Pro users getting 404 errors when storing citations
- PDF upload returning 403 Forbidden errors
- PDF export returning 404 Not Found errors
- Free plan working normally

**Root Cause:**
Next.js build cache corruption causing vendor chunks module resolution errors.

**Solution:**
1. Clear build cache:
   ```bash
   rm -rf .next
   rm -rf node_modules/.cache
   ```

2. Restart development server:
   ```bash
   npm run dev
   ```

3. For production deployment:
   ```bash
   npm run build
   ```

**Prevention:**
- Clear build cache before production deployments
- Monitor build logs for module resolution warnings
- Use the provided test scripts to verify API functionality

## 🔧 Development Issues

### Next.js Module Resolution Errors

**Symptoms:**
```
Error: Cannot find module './chunks/vendor-chunks/next.js'
Error: Cannot find module './586.js'
```

**Solution:**
1. Clean installation:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. Clear Next.js cache:
   ```bash
   rm -rf .next
   ```

3. Restart development server:
   ```bash
   npm run dev
   ```

### Lambda Function Connectivity Issues

**Symptoms:**
- API endpoints returning 500 errors
- "Citation service not available" errors
- Lambda function timeouts

**Diagnosis:**
1. Check `amplify_outputs.json` for correct Lambda URLs:
   ```bash
   cat amplify_outputs.json | grep -E "(citationFunctionUrl|pdfGeneratorUrl|pdfProcessorUrl)"
   ```

2. Test Lambda functions directly:
   ```bash
   node test/lambda_connectivity_test.js
   ```

**Solution:**
1. Redeploy Lambda functions:
   ```bash
   npx amplify sandbox
   ```

2. Verify environment variables in Lambda functions
3. Check CloudWatch logs for detailed error messages

### Database Connection Issues

**Symptoms:**
- "Failed to check citation limit" errors
- Authentication failures
- User data not loading

**Diagnosis:**
1. Check Supabase connection:
   ```bash
   # Test database connectivity
   node -e "
   const { supabase } = require('./src/lib/supabase');
   supabase.from('users').select('count').then(console.log);
   "
   ```

**Solution:**
1. Verify environment variables:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - `SUPABASE_SERVICE_ROLE_KEY`

2. Check Supabase project status
3. Verify RLS policies are correctly configured

## 🧪 Testing and Validation

### API Functionality Testing

Run comprehensive API tests:

```bash
# Test all API endpoints
node test/api_functionality_test.js

# Test Lambda connectivity
node test/lambda_connectivity_test.js

# End-to-end workflow test
node test/end_to_end_test.js
```

### Expected Test Results

**API Functionality Test:**
- All APIs should return 400/401 status codes (indicating proper validation)
- No 500 server errors should occur

**Lambda Connectivity Test:**
- Citation Function: 400 (missing required fields)
- PDF Generator: 400 (missing csl_items)
- PDF Processor: 400 (missing PDF data)

**End-to-End Test:**
- Pro plan: Should complete successfully
- Free/Plus plans: May fail on citation limit check (expected for test users)

## 🚀 Production Deployment

### Pre-Deployment Checklist

- [ ] Clear build cache: `rm -rf .next`
- [ ] Run full test suite
- [ ] Verify environment variables
- [ ] Check Lambda function URLs in `amplify_outputs.json`
- [ ] Test database connectivity
- [ ] Verify S3 bucket permissions

### Post-Deployment Verification

1. **API Health Check:**
   ```bash
   curl -X POST https://your-domain.com/api/cite \
     -H "Content-Type: application/json" \
     -d '{"test": "ping"}'
   ```
   Expected: 400 Bad Request (not 500)

2. **Lambda Function Test:**
   ```bash
   curl -X POST https://lambda-url.amazonaws.com/ \
     -H "Content-Type: application/json" \
     -d '{"test": "ping"}'
   ```

3. **Database Connection:**
   - Test user authentication
   - Verify citation limit checks
   - Test document creation

### Common Production Errors

#### Error: "Citation service not available"
- Check `amplify_outputs.json` is deployed correctly
- Verify Lambda function URLs are accessible
- Check AWS IAM permissions

#### Error: "Failed to check citation limit"
- Verify Supabase service role key
- Check RLS policies
- Ensure user exists in database

#### Error: "Authentication required"
- Check JWT token validation
- Verify Supabase auth configuration
- Test with valid user session

## 📊 Monitoring and Logging

### Key Metrics to Monitor

1. **API Response Times**
   - Citation generation: < 10s
   - PDF export: < 15s
   - PDF processing: < 30s

2. **Error Rates**
   - 4xx errors: < 5% (user errors)
   - 5xx errors: < 1% (server errors)

3. **Lambda Function Performance**
   - Cold start times
   - Memory usage
   - Timeout rates

### Log Analysis

**Next.js Logs:**
```bash
# Check for module resolution errors
grep -i "cannot find module" logs/

# Check for API errors
grep -i "error" logs/ | grep -E "(api|route)"
```

**Lambda Logs (CloudWatch):**
- Check for timeout errors
- Monitor memory usage
- Look for dependency issues

**Supabase Logs:**
- Monitor query performance
- Check for RLS policy violations
- Track authentication failures

## 🆘 Emergency Procedures

### Complete System Failure

1. **Immediate Actions:**
   - Check AWS service status
   - Verify Supabase status
   - Review recent deployments

2. **Rollback Procedure:**
   ```bash
   # Revert to last known good deployment
   git checkout <last-good-commit>
   npm run build
   npx amplify sandbox
   ```

3. **Communication:**
   - Update status page
   - Notify users via email/social media
   - Document incident for post-mortem

### Data Recovery

1. **Database Issues:**
   - Check Supabase backups
   - Verify data integrity
   - Restore from point-in-time backup if needed

2. **File Storage Issues:**
   - Check S3 bucket status
   - Verify backup policies
   - Restore files from backup if needed

## 📞 Support Contacts

- **AWS Support:** For Lambda/S3 issues
- **Supabase Support:** For database issues
- **Development Team:** For application-specific issues

## 📝 Incident Reporting

When reporting issues, include:

1. **Environment:** Development/Staging/Production
2. **Timestamp:** When the issue occurred
3. **User Impact:** How many users affected
4. **Error Messages:** Full error logs
5. **Steps to Reproduce:** Detailed reproduction steps
6. **Browser/Device:** User agent information

---

*Last Updated: 2025-09-22*
*Version: 1.0*
