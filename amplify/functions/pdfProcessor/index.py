"""
PDF文档处理Lambda函数
用于从上传的PDF文件中提取文本内容，仅供Pro用户使用
"""

import json
import os
import sys
import uuid
import boto3
import base64
import tempfile
from botocore.exceptions import ClientError
from typing import Dict, Any, Optional

# 导入PDF处理库
try:
    # Add layer path to Python path if not already there
    layer_path = '/opt/python-dependency_layer'
    if layer_path not in sys.path and os.path.exists(layer_path):
        sys.path.insert(0, layer_path)

    import PyPDF2
    from io import BytesIO
except ImportError as e:
    print(f"PyPDF2 not available: {e}", file=sys.stderr)
    PyPDF2 = None

# 初始化AWS客户端
s3_client = boto3.client('s3')

def extract_text_from_pdf(pdf_bytes: bytes) -> Dict[str, Any]:
    """
    从PDF字节数据中提取文本内容
    
    Args:
        pdf_bytes: PDF文件的字节数据
        
    Returns:
        包含提取文本、页数和其他元数据的字典
    """
    try:
        if PyPDF2:
            # 使用PyPDF2提取文本
            pdf_reader = PyPDF2.PdfReader(BytesIO(pdf_bytes))
            
            text_content = []
            page_count = len(pdf_reader.pages)
            
            for page_num, page in enumerate(pdf_reader.pages, 1):
                try:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text_content.append(f"--- Page {page_num} ---\n{page_text}")
                except Exception as e:
                    print(f"Error extracting text from page {page_num}: {e}", file=sys.stderr)
                    text_content.append(f"--- Page {page_num} ---\n[Error extracting text from this page]")
            
            extracted_text = "\n\n".join(text_content)
            
            # 如果没有提取到文本，提供有用的错误信息
            if not extracted_text.strip():
                extracted_text = """PDF文档已上传，但无法提取文本内容。

可能的原因：
1. PDF是扫描版本（图片格式），需要OCR处理
2. PDF使用了特殊编码或加密
3. PDF内容主要是图像或图表

建议：
- 如果是扫描版PDF，请使用OCR工具转换为文本
- 或者手动复制粘贴PDF中的文本内容
- 确保PDF文件没有密码保护"""
            
            return {
                "text": extracted_text,
                "page_count": page_count,
                "word_count": len(extracted_text.split()),
                "extraction_method": "PyPDF2",
                "success": True
            }
        else:
            # 备用方法：返回占位符文本
            return {
                "text": """PDF文档已成功上传到系统。

由于服务器环境限制，当前无法自动提取PDF文本内容。

请手动复制粘贴PDF中的文本内容，或者：
1. 使用在线PDF转文本工具
2. 在本地使用PDF阅读器复制文本
3. 联系技术支持获取帮助

我们正在努力改进PDF处理功能。""",
                "page_count": 1,
                "word_count": 50,
                "extraction_method": "fallback",
                "success": False
            }
            
    except Exception as e:
        print(f"Error in PDF text extraction: {e}", file=sys.stderr)
        return {
            "text": f"""PDF处理过程中发生错误：{str(e)}

请尝试：
1. 确保PDF文件没有损坏
2. 检查PDF文件大小（建议小于10MB）
3. 如果问题持续，请手动复制粘贴文本内容""",
            "page_count": 1,
            "word_count": 30,
            "extraction_method": "error",
            "success": False,
            "error": str(e)
        }

def upload_pdf_to_s3(pdf_bytes: bytes, filename: str) -> str:
    """
    将PDF文件上传到S3进行临时存储
    
    Args:
        pdf_bytes: PDF文件字节数据
        filename: 原始文件名
        
    Returns:
        S3对象的键名
    """
    s3_bucket = os.environ.get('S3_BUCKET_NAME')
    if not s3_bucket:
        raise EnvironmentError("S3_BUCKET_NAME environment variable is not set.")
    
    # 生成唯一的S3键名
    file_extension = os.path.splitext(filename)[1] or '.pdf'
    s3_key = f"pdf-uploads/{uuid.uuid4()}{file_extension}"
    
    try:
        s3_client.put_object(
            Bucket=s3_bucket,
            Key=s3_key,
            Body=pdf_bytes,
            ContentType='application/pdf',
            # 设置7天后自动删除
            Expires=7 * 24 * 60 * 60  # 7 days in seconds
        )
        
        print(f"PDF uploaded to S3: s3://{s3_bucket}/{s3_key}", file=sys.stderr)
        return s3_key
        
    except ClientError as e:
        print(f"Error uploading PDF to S3: {e}", file=sys.stderr)
        raise

def handler(event, context):
    """
    AWS Lambda处理程序，用于处理PDF文档上传和文本提取
    """
    try:
        print(f"Received event: {json.dumps(event, default=str)}", file=sys.stderr)

        # 解析请求数据
        if 'body' in event:
            if event.get('isBase64Encoded', False):
                body = base64.b64decode(event['body']).decode('utf-8')
            else:
                body = event['body']
            request_data = json.loads(body)
        else:
            request_data = event

        # 获取PDF文件数据
        pdf_data = request_data.get('pdf_data')  # Base64编码的PDF数据
        filename = request_data.get('filename', 'document.pdf')
        document_id = request_data.get('document_id')
        user_id = request_data.get('user_id')  # 用于直接调用时的用户ID
        auth_token = request_data.get('auth_token')  # 用于直接调用时的认证token

        if not pdf_data:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({'error': 'PDF data is required'})
            }

        if not document_id:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({'error': 'Document ID is required'})
            }

        # 如果提供了用户ID和认证token，进行基本验证
        # 注意：这是一个简化的验证，生产环境中应该有更严格的验证
        if user_id and auth_token:
            print(f"Direct Lambda call - User ID: {user_id}, Document ID: {document_id}")
            # TODO: 在这里可以添加更严格的用户权限验证
            # 例如验证auth_token的有效性，检查用户订阅计划等

        # 解码PDF数据
        try:
            pdf_bytes = base64.b64decode(pdf_data)
        except Exception as e:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({'error': f'Invalid PDF data encoding: {str(e)}'})
            }

        # 验证PDF文件大小（限制为10MB）
        max_size = 10 * 1024 * 1024  # 10MB
        if len(pdf_bytes) > max_size:
            return {
                'statusCode': 400,
                'headers': {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                },
                'body': json.dumps({'error': f'PDF file too large. Maximum size is {max_size // (1024*1024)}MB'})
            }

        # 上传PDF到S3进行临时存储
        try:
            s3_key = upload_pdf_to_s3(pdf_bytes, filename)
        except Exception as e:
            print(f"Failed to upload PDF to S3: {e}", file=sys.stderr)
            s3_key = None  # 继续处理，即使S3上传失败

        # 提取PDF文本
        extraction_result = extract_text_from_pdf(pdf_bytes)

        # 返回处理结果
        response_data = {
            'document_id': document_id,
            'filename': filename,
            'extracted_text': extraction_result['text'],
            'page_count': extraction_result['page_count'],
            'word_count': extraction_result['word_count'],
            'extraction_method': extraction_result['extraction_method'],
            'success': extraction_result['success'],
            's3_key': s3_key,
            'file_size_mb': round(len(pdf_bytes) / (1024 * 1024), 2)
        }

        if 'error' in extraction_result:
            response_data['extraction_error'] = extraction_result['error']

        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps(response_data)
        }

    except Exception as e:
        print(f"Error processing PDF request: {e}", file=sys.stderr)
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                'error': 'Internal server error',
                'details': str(e)
            })
        }
