"use client"

import { useEffect, useState, useRef } from "react"
import { useRouter, useParams } from "next/navigation"
import { supabase } from "@/lib/supabase"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Check, Save, FileText, Zap, Download } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"

import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { usePlan } from '@/contexts/PlanContext';
import { CitationActionDialog } from '@/components/CitationActionDialog';
import { MicrotransactionPurchase } from '@/components/MicrotransactionPurchase';
import React from 'react';

export default function DocumentEditor() {
  const router = useRouter()
  const params = useParams()
  const { user, loading: authLoading } = useAuth()
  const docId = params?.id as string
  const [content, setContent] = useState("")
  const [title, setTitle] = useState("")
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [wordCount, setWordCount] = useState(0)
  const [charCount, setCharCount] = useState(0)
  const [showTips, setShowTips] = useState(false)
  const [citationFormat, setCitationFormat] = useState<'mla' | 'apa'>('mla');
  const [citing, setCiting] = useState(false);
  const [citationResult, setCitationResult] = useState<string | null>(null);
  const [citationError, setCitationError] = useState<string | null>(null);
  const [citationHtml, setCitationHtml] = useState<string | null>(null);
  const [showCitationButton, setShowCitationButton] = useState(false);
  const hiddenRef = useRef<HTMLDivElement>(null);
  const [showCopySuccess, setShowCopySuccess] = useState(false);
  const { currentPlan, availableCitations, refreshCitations } = usePlan();
  // Add state for PDF exporting
  const [exportingPdf, setExportingPdf] = useState(false);
  // Add state for citation action dialog
  const [showCitationActionDialog, setShowCitationActionDialog] = useState(false);
  const [pendingCitationData, setPendingCitationData] = useState<any>(null);
  // Add state for automatic microtransaction dialog
  const [showAutoMicrotransactionDialog, setShowAutoMicrotransactionDialog] = useState(false);
  const [hasShownZeroCitationsDialog, setHasShownZeroCitationsDialog] = useState(false);
  // Add state for PDF drag and drop
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessingPdf, setIsProcessingPdf] = useState(false);
  const [pdfFileName, setPdfFileName] = useState<string | null>(null);

  useEffect(() => {
    if (authLoading) return; // Wait for auth to finish loading
    if (!user) {
      router.push("/get-started")
      return
    }

    // Validate document ID
    if (!docId || typeof docId !== 'string' || docId.trim() === '') {
      setError("Invalid document ID")
      setLoading(false)
      return
    }
    
    // Check if docId is a valid UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(docId)) {
      console.error("Invalid document ID format:", docId);
      setError("Invalid document ID format")
      setLoading(false)
      return
    }

        const fetchDocument = async () => {
      try {
        setLoading(true)
        setError(null) // Clear any previous errors
        
        console.log("Fetching document:", {
          docId,
          userId: user?.id,
          userEmail: user?.email,
          authLoading
        })
        
        // Check if we have a valid session
        const { data: sessionData } = await supabase.auth.getSession()
        console.log("Session check:", {
          hasSession: !!sessionData.session,
          sessionUser: sessionData.session?.user?.id
        })
        
        const { data, error } = await supabase
          .from("documents")
          .select("title, content, user_id, word_count, deleted_at")
          .eq("id", docId)
          .eq("user_id", user.id)
          .single()

        if (error) {
          console.error("Error fetching document:", {
            error,
            errorCode: error.code,
            errorMessage: error.message,
            errorDetails: error.details,
            docId,
            userId: user?.id,
            userEmail: user?.email
          })
          
          if (error.code === 'PGRST116') {
            setError("Document not found or you don't have permission to access it.")
          } else if (error.code === 'PGRST301') {
            setError("Authentication required. Please log in again.")
          } else {
            setError(`Failed to load document: ${error.message || 'Unknown error'}`)
          }
          setLoading(false)
          return
        }

        if (data) {
          // Check if document is deleted
          if (data.deleted_at) {
            setError("This document has been moved to trash. You can restore it from the trash page.")
            setLoading(false)
            return
          }

          setTitle(data.title || "")
          setContent(data.content || "")
          updateCounts(data.content || "")
        }
      } catch (err) {
        console.error("Unexpected error fetching document:", err)
        setError("An unexpected error occurred. Please try again.")
      } finally {
        setLoading(false)
      }
    }

    // Add a small delay to prevent race conditions after redirects
    const timeoutId = setTimeout(() => {
      // Double-check that user is fully loaded
      if (!user?.id) {
        console.log("User not fully loaded yet, skipping document fetch")
        return
      }
      fetchDocument()
    }, 100)

    return () => clearTimeout(timeoutId)
  }, [docId, user, authLoading, router])

  useEffect(() => {
    const autoSave = setInterval(() => {
      if (!saving && !loading && (content !== "" || title !== "")) {
        handleSave()
      }
    }, 30000)

    return () => clearInterval(autoSave)
  }, [content, title, saving, loading])

  // Fetch citations_html on load
  useEffect(() => {
    if (!user || !docId) return;
    const fetchCitations = async () => {
      const { data, error } = await supabase
        .from('documents')
        .select('citations_html')
        .eq('id', docId)
        .eq('user_id', user.id)
        .single();
      if (!error && data && data.citations_html) {
        setCitationHtml(data.citations_html);
        setShowCitationButton(true);
      }
    };
    fetchCitations();
  }, [user, docId]);

  // Handle microtransaction success redirect
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const canceled = urlParams.get('canceled');
    const sessionId = urlParams.get('session_id');
    const productId = urlParams.get('product_id');
    const source = urlParams.get('source');
    
    // Handle successful payment from document page
    if (success === 'true' && sessionId && productId && user && source === 'document') {
      // Refresh citation count
      refreshCitations();
      
      // Show success message
      setCitationError(null);
      setCitationResult('Citations purchased successfully! Your citation count has been updated.');
      
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
      
      // Show success message for a few seconds
      setTimeout(() => {
        setCitationResult(null);
      }, 5000);
    }
    
    // Handle canceled payment from document page
    if (canceled === 'true' && source === 'document') {
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
      
      // Optionally show a message that payment was canceled
      setCitationError('Payment was canceled. You can try again anytime.');
      
      // Clear the error after a few seconds
      setTimeout(() => {
        setCitationError(null);
      }, 5000);
    }
  }, [user, refreshCitations]);

  // Monitor citation count and show auto-purchase dialog when reaching 0
  useEffect(() => {
    // Reset the flag when citations become available again
    if (availableCitations > 0) {
      setHasShownZeroCitationsDialog(false);
    }
    
    // Show dialog when citations reach 0 (only once)
    if (currentPlan !== 'pro' && availableCitations === 0 && !showAutoMicrotransactionDialog && !citing && !hasShownZeroCitationsDialog) {
      // Small delay to ensure this doesn't trigger immediately on page load
      const timeoutId = setTimeout(() => {
        setShowAutoMicrotransactionDialog(true);
        setHasShownZeroCitationsDialog(true);
      }, 1000);
      
      return () => clearTimeout(timeoutId);
    }
  }, [availableCitations, currentPlan, showAutoMicrotransactionDialog, citing, hasShownZeroCitationsDialog]);

  // Update citations_html in Supabase after generating a new citation
  const saveCitationToSupabase = async (citationHtml: string) => {
    if (!user || !docId) return;
    await supabase
      .from('documents')
      .update({ citations_html: citationHtml })
      .eq('id', docId)
      .eq('user_id', user.id);
  };

  // Function to count words properly, treating hyphenated words as separate words
  const countWords = (text: string): number => {
    if (!text.trim()) return 0;
    
    // Split by whitespace first
    const words = text.trim().split(/\s+/);
    
    // For each word, split by hyphens and count each part
    let totalWords = 0;
    for (const word of words) {
      if (word.includes('-')) {
        // Split by hyphens and count each part (filter out empty strings)
        const parts = word.split('-').filter(part => part.length > 0);
        totalWords += parts.length;
      } else {
        totalWords += 1;
      }
    }
    
    return totalWords;
  };

  const updateCounts = (text: string) => {
    const words = countWords(text);
    setWordCount(words);
    setCharCount(text.length);
  }

  // Add state for word count and warning
  const [wordLimitWarning, setWordLimitWarning] = useState("");

  // Update word limit based on currentPlan
  let wordLimit = Infinity;
  if (currentPlan === 'free') wordLimit = 150;
  else if (currentPlan === 'plus') wordLimit = 500;
  // Pro plan has unlimited words

  // Update word count and enforce limit (truncate if over limit)
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const text = e.target.value;
    const wordCount = countWords(text);
    let newText = text;
    
    if (wordCount > wordLimit) {
      // Truncate text to word limit while preserving hyphenated words
      const words = text.trim().split(/\s+/);
      let currentWordCount = 0;
      let truncatedWords = [];
      
      for (const word of words) {
        let wordParts = 1;
        if (word.includes('-')) {
          wordParts = word.split('-').filter(part => part.length > 0).length;
        }
        
        if (currentWordCount + wordParts <= wordLimit) {
          truncatedWords.push(word);
          currentWordCount += wordParts;
        } else {
          break;
        }
      }
      
      newText = truncatedWords.join(' ');
      setWordLimitWarning(`Word limit exceeded: Only the first ${wordLimit} words are kept for your plan.`);
    } else {
      setWordLimitWarning("");
    }
    
    setWordCount(countWords(newText));
    setContent(newText);
  };

  const handleSave = async () => {
    if (!user || !docId) return

    setSaving(true)
    setError(null)

    const { error } = await supabase
      .from("documents")
      .update({
        title: title.trim() || "Untitled Document",
        content: content,
        word_count: wordCount,
        updated_at: new Date().toISOString(),
      })
      .eq("id", docId)
      .eq("user_id", user.id)
      .single()

    if (error) {
      console.error("Error saving document:", error)
      setError("Failed to save document. Please try again.")
    } else {
      setSaved(true)
      setTimeout(() => setSaved(false), 2000)
    }
    setSaving(false)
  }

  const handleStartCiting = async () => {
    // Check if user has 0 citations and show purchase dialog
    if (currentPlan !== 'pro' && availableCitations === 0) {
      setShowAutoMicrotransactionDialog(true);
      return;
    }

    setCiting(true);
    setCitationResult(null);
    setCitationError(null);
    setShowCitationButton(false);
    setCitationHtml(null);
    // 修复后的前端引用处理代码
    try {
      const res = await fetch('/api/cite', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title,
          content,
          citation_format: citationFormat,
          plan: currentPlan,
          html: true, // always request HTML output for popup/copy
          userId: user?.id
        }),
      });
      
      const data = await res.json();
      
      if (res.ok) {
        // 打印调试信息
        console.log('Citation API response:', {
          hasHtml: !!data.citation_html,
          htmlLength: data.citation_html?.length,
          htmlPreview: data.citation_html?.substring(0, 100),
          currentPlan,
          citationCount: data.citation_count
        });
        
        // 修复：使用正确的字段名 data.citation_html 而不是 citationHtml
        // For Plus/Pro plans, show action dialog if citations exist
        if ((currentPlan === 'plus' || currentPlan === 'pro') && data.citation_html) {
          setPendingCitationData(data);
          setShowCitationActionDialog(true);
          setCiting(false);
          return;
        }
        
        // For Free plan or first citation, proceed normally
        await handleCitationAction(data, 'replace');
      } else {
        console.error('Citation API error:', data);
        if (data.limitReached) {
          setCitationError('Citation limit reached. Please upgrade your plan or try again tomorrow.');
        } else {
          setCitationError(data.error || 'Failed to generate citation.');
        }
      }
    } catch (err : any) {
      console.error('Citation request failed:', err);
      setCitationError(err.message || 'An error occurred.');
    } finally {
      setCiting(false);
    }
  };

  const handleCitationAction = async (data: any, action: 'replace' | 'add') => {
    try {
      // Get authentication token
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      // Store citations in database first
      console.log('Citation data received:', {
        hasCitations: !!data.citations,
        citationsLength: data.citations?.length || 0,
        citationsPreview: data.citations?.slice(0, 2) || [],
        action: action,
        currentPlan: currentPlan
      });

      if (data.citations && data.citations.length > 0) {
        console.log('Storing citations to database...');
        const storeResponse = await fetch('/api/citations/store', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
          },
          body: JSON.stringify({
            documentId: docId,
            userId: user?.id,
            citations: data.citations,
            action: action
          }),
        });

        if (!storeResponse.ok) {
          const errorText = await storeResponse.text();
          console.error('Failed to store citations:', errorText);
          throw new Error(`Failed to store citations: ${storeResponse.status}`);
        }

        const storeResult = await storeResponse.json();
        console.log('Citations stored successfully:', storeResult);
      } else {
        console.warn('No citations to store:', {
          hasCitations: !!data.citations,
          citationsLength: data.citations?.length || 0,
          dataKeys: Object.keys(data)
        });
      }

      // For Plus/Pro plans, retrieve all citations and format them together
      if (currentPlan === 'plus' || currentPlan === 'pro') {
        const allCitationsResponse = await fetch(`/api/citations/get?documentId=${docId}`, {
          headers: {
            ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
          },
        });

        if (!allCitationsResponse.ok) {
          const errorText = await allCitationsResponse.text();
          console.error('Failed to get citations:', errorText);
          throw new Error(`Failed to get citations: ${allCitationsResponse.status}`);
        }

        const allCitationsData = await allCitationsResponse.json();
        console.log('Retrieved citations data:', allCitationsData);

        if (allCitationsData.citations && allCitationsData.citations.length > 0) {
          // Format all citations together using the Python script
          const formatResponse = await fetch('/api/citations/format', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              citations: allCitationsData.citations,
              citation_format: citationFormat,
              html: true
            }),
          });

          if (!formatResponse.ok) {
            const errorText = await formatResponse.text();
            console.error('Failed to format citations:', errorText);
            throw new Error(`Failed to format citations: ${formatResponse.status}`);
          }

          const formatData = await formatResponse.json();
          console.log('Format response data:', formatData);

          if (formatData.citation_html) {
            setCitationHtml(formatData.citation_html);
            setShowCitationButton(true);
            await saveCitationToSupabase(formatData.citation_html);
          } else {
            console.warn('No citation_html in format response:', formatData);
            // Still show the button even if formatting failed
            setShowCitationButton(true);
          }
        } else {
          console.warn('No citations found in response:', allCitationsData);
          // Still show the button even if no citations found
          setShowCitationButton(true);
        }
      } else {
        // For Free plan, use the original citation HTML
        if (data.citation_html) {
          setCitationHtml(data.citation_html);
          setShowCitationButton(true);
          await saveCitationToSupabase(data.citation_html);
        }
      }

      if (data.citation) {
        setCitationResult(data.citation);
      }

      // Refresh citation count after successful generation
      await refreshCitations();

      // Ensure citation button is always visible after successful operation
      setShowCitationButton(true);

    } catch (err: any) {
      console.error('Citation action error:', err);
      setCitationError(err.message || 'An error occurred.');
      // Even on error, ensure the citation button remains visible
      setShowCitationButton(true);
    }
  };

  const copyAsHTML = async (citationHtml: string) => {
    // 1) Parse the HTML to extract individual citations
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = citationHtml;
    const citationDivs = Array.from(tempDiv.querySelectorAll('div[style*="text-indent"]'));
    
    // 2) Convert each citation to proper format with HTML tags
    const citations = citationDivs.map(div => {
      let content = div.innerHTML;
      // Convert span classes to proper HTML tags
      content = content.replace(/<span class="italic-title">(.*?)<\/span>/g, '<em>$1</em>');
      content = content.replace(/<span class="quoted-title">(.*?)<\/span>/g, '"$1"');
      // Remove any remaining HTML tags except basic ones
      content = content.replace(/<(?!\/?(em|b|br|p|i))[^>]*>/g, '');
      return content.trim();
    });
    
    try {
      // Create a temporary container with minimal styling
      const container = document.createElement('div');
      container.style.position = 'absolute';
      container.style.left = '-9999px';
      container.style.top = '-9999px';
      container.style.fontFamily = 'Times New Roman, Times, serif';
      container.style.margin = '0';
      container.style.padding = '0';
      container.style.border = 'none';
      container.style.background = 'transparent';
      
      // Create citation paragraphs with hanging indent like mybib.com
      citations.forEach(citation => {
        const p = document.createElement('p');
        p.style.wordBreak = 'break-word';
        p.style.lineHeight = '200%';
        p.style.marginBottom = '0em';
        p.style.marginTop = '0px';
        p.style.marginLeft = '36pt';
        p.style.textIndent = '-36pt';
        p.innerHTML = citation;
        container.appendChild(p);
      });
      
      document.body.appendChild(container);
      
      // Select and copy the content
      const range = document.createRange();
      range.selectNodeContents(container);
      const sel = window.getSelection();
      sel?.removeAllRanges();
      sel?.addRange(range);
      
      // @ts-ignore - execCommand is deprecated but still functional
      const success = document.execCommand('copy');
      
      // Clean up
      sel?.removeAllRanges();
      document.body.removeChild(container);
      
      if (success) {
        console.log('Citations copied with HTML formatting!');
        return true;
      } else {
        console.error('execCommand failed');
        return false;
      }
    } catch (err) {
      console.error('HTML copy failed:', err);
      return false;
    }
  };



  const handleCopyCitation = async () => {
    if (citationHtml) {
      // Use HTML formatting with hanging indent for reliable copying
      await copyAsHTML(citationHtml);
      
      // feedback to user
      setShowCopySuccess(true);
      setTimeout(() => setShowCopySuccess(false), 2000);
    }
  };

  // Export as PDF handler - 直接下载PDF文件
  const handleExportPdf = async () => {
    setExportingPdf(true);
    setCitationError(null);
    try {
      // 获取认证token
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      if (!authToken) {
        setCitationError('Authentication required. Please log in again.');
        setExportingPdf(false);
        return;
      }

      const res = await fetch('/api/citations/export-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify({
          documentId: docId,
          citationFormat: citationFormat
        }),
      });

      if (res.ok) {
        const data = await res.json();
        if (data.html_content) {
          // 直接下载HTML文件而不是显示模态框
          const blob = new Blob([data.html_content], { type: 'text/html' });
          const url = URL.createObjectURL(blob);

          // 创建下载链接
          const a = document.createElement('a');
          a.href = url;
          a.download = `citations-${data.citation_format || citationFormat}.html`;
          document.body.appendChild(a);
          a.click();

          // 清理
          document.body.removeChild(a);
          URL.revokeObjectURL(url);

          // 显示成功消息
          setCitationError(null);
          // 可以添加成功提示
          console.log('PDF exported successfully');
        } else {
          setCitationError('Citation content not available.');
        }
      } else {
        const err = await res.json();
        setCitationError(err.error || 'Failed to export PDF.');
      }
    } catch (err: any) {
      setCitationError(err.message || 'An error occurred.');
    } finally {
      setExportingPdf(false);
    }
  };



  // PDF processing functions
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const pdfFile = files.find(file => file.type === 'application/pdf');
    
    if (pdfFile) {
      await processPdfFile(pdfFile);
    } else {
      setCitationError('Please drop a PDF file.');
    }
  };

  const processPdfFile = async (file: File) => {
    // 检查用户权限 - Plus和Pro用户可以使用PDF处理功能
    if (currentPlan !== 'plus' && currentPlan !== 'pro') {
      setCitationError('PDF processing is only available for Plus and Pro users. Please upgrade your plan.');
      return;
    }

    // 检查文件大小限制 (10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      setCitationError('PDF file is too large. Maximum size is 10MB.');
      return;
    }

    setIsProcessingPdf(true);
    setCitationError(null);
    setPdfFileName(file.name);

    try {
      const formData = new FormData();
      formData.append('pdf', file);
      formData.append('documentId', docId);

      // 获取认证token，如果session过期则尝试刷新
      let { data: { session }, error: sessionError } = await supabase.auth.getSession();

      // 如果session不存在或即将过期，尝试刷新
      if (!session || !session.access_token) {
        console.log('PDF Upload - No session, attempting refresh...');
        const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
        if (refreshData.session) {
          session = refreshData.session;
          sessionError = null;
        } else {
          sessionError = refreshError;
        }
      }

      console.log('PDF Upload - Session check:', {
        hasSession: !!session,
        hasAccessToken: !!session?.access_token,
        sessionError: sessionError,
        userId: session?.user?.id,
        expiresAt: session?.expires_at
      });

      if (!session || !session.access_token) {
        setCitationError('Authentication required. Please log in again.');
        return;
      }

      const authToken = session.access_token;

      // 尝试使用本地API路由，如果失败则使用直接Lambda调用
      let response;
      try {
        response = await fetch('/api/documents/process-pdf', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${authToken}`,
          },
          body: formData,
        });

        // 如果是CloudFront 403错误，尝试直接调用Lambda
        if (response.status === 403 && response.headers.get('server') === 'CloudFront') {
          console.log('PDF Upload - CloudFront 403 detected, trying direct Lambda call...');
          throw new Error('CloudFront 403 - fallback to Lambda');
        }
      } catch (error) {
        console.log('PDF Upload - API route failed, trying direct Lambda call:', error);

        // 直接调用Lambda函数作为备用方案
        try {
          // 转换FormData为JSON格式，因为Lambda期望JSON
          const pdfBuffer = await file.arrayBuffer();
          const pdfBase64 = btoa(String.fromCharCode(...new Uint8Array(pdfBuffer)));

          const lambdaPayload = {
            pdf_data: pdfBase64,
            filename: file.name,
            document_id: docId,
            user_id: session.user.id,
            auth_token: authToken
          };

          response = await fetch('https://wfmkvwmipsb2hd3x4o3vxrts3q0wdoly.lambda-url.us-east-2.on.aws/', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(lambdaPayload),
          });
        } catch (lambdaError) {
          console.error('PDF Upload - Lambda call also failed:', lambdaError);
          throw error; // 抛出原始错误
        }
      }

      console.log('PDF Upload - Response status:', response.status);

      if (response.ok) {
        const responseData = await response.json();
        console.log('PDF Upload - Response data:', responseData);

        // 处理不同的响应格式（API路由 vs Lambda直接调用）
        let extractedText, newWordCount;

        if (responseData.extractedText) {
          // API路由响应格式
          extractedText = responseData.extractedText;
          newWordCount = responseData.wordCount;
        } else if (responseData.extracted_text) {
          // Lambda直接调用响应格式
          extractedText = responseData.extracted_text;
          newWordCount = responseData.word_count;
        } else {
          console.error('PDF Upload - Unexpected response format:', responseData);
          setCitationError('Unexpected response format from PDF processing service.');
          return;
        }

        console.log('PDF Upload - Success:', {
          hasExtractedText: !!extractedText,
          textLength: extractedText?.length || 0,
          wordCount: newWordCount,
          responseFormat: responseData.extractedText ? 'API' : 'Lambda'
        });

        if (extractedText && extractedText.trim()) {
          setContent(extractedText);
          setWordCount(newWordCount);
          setCharCount(extractedText.length);
          setSaved(false); // Mark as needing to save
          setCitationError(null);
        } else {
          setCitationError('No text could be extracted from the PDF. Please try a different file or copy the text manually.');
        }
      } else {
        const error = await response.json().catch(() => ({ error: 'Failed to process PDF' }));
        console.error('PDF Upload - Error response:', {
          status: response.status,
          error: error,
          headers: Object.fromEntries(response.headers.entries())
        });

        if (response.status === 403) {
          setCitationError('Access denied. Please check your subscription plan and try logging in again.');
        } else if (response.status === 401) {
          setCitationError('Authentication failed. Please log in again.');
        } else {
          setCitationError(error.error || `Failed to process PDF (${response.status})`);
        }
      }
    } catch (err: any) {
      console.error('PDF processing error:', err);
      setCitationError(err.message || 'An error occurred while processing the PDF.');
    } finally {
      setIsProcessingPdf(false);
    }
  };

  const handleFileInput = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      await processPdfFile(file);
    }
    // Reset the input
    e.target.value = '';
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 border-b-2 border-amber-600 mx-auto mb-4"></div>
          <p className="text-amber-700">Loading document...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error: {error}</p>
          <Button onClick={() => router.push("/dashboard")} className="bg-amber-600 hover:bg-amber-700 text-white">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50">
      {/* Left Sidebar with Hoverable Tips */}
      <div className="relative">
        {/* Hover Container - wraps both icon and panel */}
        <div 
          className="fixed left-4 top-1/2 transform -translate-y-1/2 flex items-start"
          onMouseEnter={() => setShowTips(true)}
          onMouseLeave={() => setShowTips(false)}
        >
          {/* Fixed Icon Container */}
          <div className="flex flex-col items-center cursor-pointer group">
            {/* Lightbulb Icon */}
            <div className="bg-white/90 rounded-full p-3 w-12 h-12 border border-amber-200 hover:bg-white group-hover:shadow-xl transition-all duration-300 flex items-center justify-center">
              <span className="text-3xl text-amber-500">💡</span>
            </div>
            <span className="text-xs text-amber-700 font-medium group-hover:text-amber-900 transition-colors">Quick Tips</span>
          </div>

          {/* Tips Panel */}
          <div
            className={`ml-4 bg-white/95 border border-amber-200 rounded-xl shadow-2xl p-8 w-80 max-h-96 overflow-y-auto transition-all duration-300 ease-in-out z-40 ${showTips ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4 pointer-events-none'}`}
          >
            <h3 className="text-xl font-bold text-amber-900 flex items-center mb-6">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-amber-600 mr-3">
                <path d="M9 12l2 2 4-4"/>
                <path d="M21 12c-1 0-2-1-2-2s1-2 2-2"/>
                <path d="M3 12c1 0 2-1 2-2s-1-2-2-2"/>
                <path d="M12 3c0 1-1 2-2 2s-2-1-2-2"/>
                <path d="M12 21c0-1 1-2 2-2s2 1 2 2"/>
              </svg>
              Writing Tips
            </h3>
            <ul className="text-amber-800 text-base space-y-4">
              <li className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-amber-500 rounded-full mt-2"></div>
                <span className="leading-relaxed">Include in-text citations as you write if you have them - CiteAI will detect them automatically</span>
              </li>
              <li className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-amber-500 rounded-full mt-2"></div>
                <span className="leading-relaxed">Use quotation marks around direct quotes for better citation detection if possible</span>
              </li>
              <li className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-amber-500 rounded-full mt-2"></div>
                <span className="leading-relaxed">Mention author names and publication years to improve accuracy</span>
              </li>
              <li className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-amber-500 rounded-full mt-2"></div>
                <span className="leading-relaxed">Click "Start Citing" to generate citations and bibliography</span>
              </li>
              <li className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-2 h-2 bg-amber-500 rounded-full mt-2"></div>
                <span className="leading-relaxed">Save your document regularly to preserve your work</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Main Editor Area */}
      <div className="flex-1 flex justify-center items-start py-12 px-2 md:px-8 lg:px-16">
        <div className="bg-white/90 rounded-2xl shadow-lg p-8 w-full max-w-3xl border border-amber-100 min-h-[90vh] flex flex-col">
          <div className="flex items-center justify-between mb-4">
            <span className="text-amber-900 font-semibold">Plan: {currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1)}</span>
          </div>
          <input
            className="w-full text-4xl font-bold mb-8 bg-transparent outline-none placeholder-amber-300 text-amber-900 font-sans"
            value={title}
            onChange={e => setTitle(e.target.value)}
            placeholder="Enter your document title..."
            style={{ minHeight: 56 }}
          />
          {/* Plus and Pro users get enhanced content input options */}
          {(currentPlan === 'plus' || currentPlan === 'pro') ? (
            <div className="w-full flex-1">
              {/* PDF Import Section - Always visible for Pro users */}
              <div className="mb-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <svg className="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 className="text-sm font-semibold text-amber-900">PDF Document Import</h3>
                    <span className="text-xs bg-amber-200 text-amber-800 px-2 py-1 rounded-full">Plus & Pro Feature</span>
                  </div>
                  <label className="cursor-pointer bg-amber-600 hover:bg-amber-700 text-white px-3 py-1.5 rounded-md text-sm transition-colors">
                    Choose PDF File
                    <input
                      type="file"
                      accept=".pdf"
                      onChange={handleFileInput}
                      className="hidden"
                    />
                  </label>
                </div>
                <p className="text-xs text-amber-700 mb-2">
                  Upload a PDF document to automatically extract its text content. Recommended for papers over 10 pages.
                </p>
                {isProcessingPdf && (
                  <div className="flex items-center gap-2 text-amber-700">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-amber-600"></div>
                    <span className="text-sm">Processing {pdfFileName}...</span>
                  </div>
                )}
              </div>

              {/* Content Input Area with Drag & Drop */}
              <div
                className={`w-full border-2 border-dashed rounded-lg transition-colors ${
                  isDragOver
                    ? 'border-amber-500 bg-amber-50'
                    : 'border-amber-200 bg-transparent'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                {/* Empty state with drag & drop instructions */}
                {!content && !isProcessingPdf && (
                  <div className="flex flex-col items-center justify-center h-48 text-center p-6">
                    <div className="text-amber-600 mb-3">
                      <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                    </div>
                    <h3 className="text-base font-semibold text-amber-900 mb-2">
                      Start Writing or Drop a PDF
                    </h3>
                    <p className="text-sm text-amber-700">
                      Type your content below or drag & drop a PDF file here
                    </p>
                  </div>
                )}
              
              {/* Processing indicator */}
              {isProcessingPdf && (
                <div className="flex flex-col items-center justify-center h-64 text-center p-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-amber-600 mb-4"></div>
                  <p className="text-amber-700">Processing {pdfFileName}...</p>
                </div>
              )}
              
                {/* Text editor */}
                <textarea
                  className="w-full flex-1 text-lg md:text-xl border-0 bg-transparent outline-none text-amber-800 placeholder-amber-300 resize-none font-serif leading-relaxed p-4"
                  value={content}
                  onChange={handleContentChange}
                  placeholder="Start writing your essay or research paper here..."
                  style={{ minHeight: 500 }}
                  maxLength={10000} // fallback safeguard
                />
              </div>
            </div>
          ) : (
            <textarea
              className="w-full flex-1 text-lg md:text-xl border-0 bg-transparent outline-none text-amber-800 placeholder-amber-300 resize-none font-serif leading-relaxed"
              value={content}
              onChange={handleContentChange}
              placeholder="Start writing your essay or research paper here..."
              style={{ minHeight: 500 }}
              maxLength={10000} // fallback safeguard
            />
          )}
          <div className="text-right text-sm text-amber-700 mt-1">
            {wordCount} / {wordLimit === Infinity ? '∞' : wordLimit} words
            {wordLimitWarning && <span className="text-red-600 ml-2">{wordLimitWarning}</span>}
          </div>
        </div>
      </div>
      {/* Right Sidebar */}
      <aside className="w-full max-w-xs min-w-[260px] bg-white/80 border-l border-amber-100 flex flex-col items-center py-10 px-6 gap-6 shadow-lg z-10">
        <div className="flex items-center gap-2 mb-8">
          <FileText className="h-7 w-7 text-amber-600" />
          <span className="text-2xl font-bold text-amber-900">CiteAI</span>
        </div>
        <nav className="flex flex-col gap-2 w-full">
          <Button variant="ghost" className="justify-start w-full text-amber-900 font-semibold" onClick={() => router.push("/dashboard")}> <ArrowLeft className="h-5 w-5 mr-2" /> Home </Button>
          <Button variant="ghost" className="justify-start w-full text-amber-900 font-semibold bg-amber-50"> <FileText className="h-5 w-5 mr-2" /> Document Editor </Button>
        </nav>
        <div className="w-full flex flex-col gap-2 mt-4">
          <label className="font-semibold text-amber-900">Citation Format</label>
          <select
            className="border border-amber-200 rounded px-2 py-1 text-amber-900 bg-white"
            value={citationFormat}
            onChange={e => setCitationFormat(e.target.value as 'mla' | 'apa')}
            disabled={citing}
          >
            <option value="mla">MLA</option>
            <option value="apa">APA</option>
          </select>
        </div>
        
        {/* Citation Counter */}
        {currentPlan !== 'pro' && (
          <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <span className="text-amber-800">
                Citations remaining today: <strong>{availableCitations}</strong>
              </span>
              {currentPlan === 'free' && (
                <span className="text-amber-600 text-xs">
                  Free: 3/month | Plus: 20/day | Pro: Unlimited
                </span>
              )}
            </div>
          </div>
        )}
        
        {/* Microtransaction Purchase - only show for free and plus users */}
        {(currentPlan === 'free' || currentPlan === 'plus') && (
          <MicrotransactionPurchase 
            documentId={docId}
            onPurchaseSuccess={() => {
              refreshCitations();
            }}
          />
        )}
        <Button
          className="w-full bg-amber-600 hover:bg-amber-700 text-white font-semibold flex items-center gap-2 mt-4"
          onClick={handleStartCiting}
          disabled={citing}
        >
          {citing ? (
            <span className="animate-spin h-5 w-5 mr-2">🔄</span>
          ) : (
            <Zap className="h-5 w-5" />
          )}
          {citing ? 'Generating...' : 'Start Citing'}
        </Button>
        {/* Citations dialog for all plans */}
        {showCitationButton && citationHtml && (
          <Dialog>
            <DialogTrigger asChild>
              <Button className="w-full mt-4 bg-amber-700 hover:bg-amber-800 text-white font-semibold">Your Citations</Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh]">
              <DialogHeader>
                <DialogTitle>Your Citations</DialogTitle>
              </DialogHeader>
              {/* Visually display one or more citations, parsed from citationHtml */}
              <div
                style={{
                  fontFamily: 'Times New Roman, Times, serif',
                  fontSize: '16px',
                  background: '#fff',
                  padding: '1em',
                  borderRadius: '8px',
                  maxWidth: '700px',
                  maxHeight: '60vh',
                  overflowY: 'auto',
                  overflowX: 'auto',
                }}
              >
                {(() => {
                  const tempDiv = document.createElement('div');
                  tempDiv.innerHTML = citationHtml;
                  const heading = tempDiv.querySelector('h1')?.textContent || '';
                  const citationDivs = Array.from(tempDiv.querySelectorAll('div[style*="text-indent"]'));
                  return (
                    <>
                      {heading && <div style={{ textAlign: 'center', fontWeight: 'bold', fontSize: '18px', marginBottom: '1em' }}>{heading}</div>}
                      <div>
                        {citationDivs.map((div, idx) => (
                          <div
                            key={idx}
                            style={{
                              textIndent: '-0.5in',
                              marginLeft: '0.5in',
                              marginBottom: '0.2em',
                              textAlign: 'justify',
                              wordBreak: 'break-all',
                              overflowWrap: 'anywhere',
                              lineHeight: 2,
                            }}
                            dangerouslySetInnerHTML={{ __html: div.innerHTML }}
                          />
                        ))}
                      </div>
                    </>
                  );
                })()}
              </div>
              {/* Hidden container for copying HTML */}
              <div
                ref={hiddenRef}
                id="hiddenCitations"
                style={{ position: 'absolute', left: '-9999px', top: '-9999px' }}
              >
                {(() => {
                  const tempDiv = document.createElement('div');
                  tempDiv.innerHTML = citationHtml;
                  const citationDivs = Array.from(tempDiv.querySelectorAll('div[style*="text-indent"]'));
                  return citationDivs.map((div, idx) => {
                    let content = div.innerHTML;
                    // Convert span classes to proper HTML tags
                    content = content.replace(/<span class="italic-title">(.*?)<\/span>/g, '<em>$1</em>');
                    content = content.replace(/<span class="quoted-title">(.*?)<\/span>/g, '"$1"');
                    // Remove any remaining HTML tags except basic ones
                    content = content.replace(/<(?!\/?(em|b|br|p))[^>]*>/g, '');
                    
                    return (
                      <p
                        key={idx}
                        style={{
                          marginLeft: "0.5in",
                          textIndent: "-0.5in",
                          lineHeight: 2,
                          fontFamily: "Times New Roman, Times, serif",
                          fontSize: "12pt",
                        }}
                        dangerouslySetInnerHTML={{ __html: content }}
                      />
                    );
                  });
                })()}
              </div>
              <div className="flex flex-col gap-2 mt-4">
                <Button
                  onClick={handleCopyCitation}
                  className="w-full"
                >
                  Copy All Citations
                </Button>
                {/* Export PDF button - only for Plus and Pro users */}
                {(currentPlan === 'plus' || currentPlan === 'pro') && (
                  <Button
                    onClick={handleExportPdf}
                    disabled={exportingPdf}
                    className="w-full"
                  >
                    {exportingPdf ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Exporting...
                      </>
                    ) : (
                      <>
                        <Download className="h-4 w-4 mr-2" />
                        Export PDF
                      </>
                    )}
                  </Button>
                )}
              </div>
              {showCopySuccess && (
                <div className="text-green-700 mt-2">Copied!</div>
              )}
            </DialogContent>
          </Dialog>
        )}
        
        {/* Citation Action Dialog for Plus/Pro plans */}
        <CitationActionDialog
          isOpen={showCitationActionDialog}
          onClose={() => {
            setShowCitationActionDialog(false);
            setPendingCitationData(null);
          }}
          onReplace={async () => {
            setShowCitationActionDialog(false);
            setCiting(true); // Show loading state
            if (pendingCitationData) {
              await handleCitationAction(pendingCitationData, 'replace');
              // 确保引用按钮显示
              setShowCitationButton(true);
            }
            setPendingCitationData(null);
            setCiting(false); // Hide loading state
          }}
          onAdd={async () => {
            setShowCitationActionDialog(false);
            setCiting(true); // Show loading state
            if (pendingCitationData) {
              await handleCitationAction(pendingCitationData, 'add');
              // 确保引用按钮显示
              setShowCitationButton(true);
            }
            setPendingCitationData(null);
            setCiting(false); // Hide loading state
          }}
          loading={citing}
        />
        
        {/* All plans now use the same HTML formatting - removed old free plan textarea */}
        {citationError && (
          <div className="w-full mt-4 p-2 border border-red-200 rounded bg-red-50 text-red-900">
            {citationError}
          </div>
        )}
        <Button className="w-full bg-amber-100 text-amber-900 hover:bg-amber-200 border border-amber-300 font-semibold flex items-center gap-2" onClick={handleSave} disabled={saving}>
          {saving ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-amber-900"></div>
          ) : saved ? (
            <Check className="h-5 w-5" />
          ) : (
            <Save className="h-5 w-5" />
          )}
          {saving ? "Saving..." : saved ? "Saved!" : "Save Document"}
        </Button>
        <div className="w-full mt-8 p-4 rounded-xl bg-amber-50 border border-amber-100 text-amber-900 text-sm flex flex-col gap-1">
          <div>Words: <span className="font-bold">{wordCount}</span></div>
          <div>Characters: <span className="font-bold">{charCount}</span></div>
        </div>
      </aside>

      {/* Automatic Microtransaction Dialog */}
      <Dialog open={showAutoMicrotransactionDialog} onOpenChange={setShowAutoMicrotransactionDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-amber-600" />
              Out of Citations
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              You've used all your available citations. Purchase more citations to continue generating citations for your document.
            </p>
            
            <div className="space-y-4">
              <MicrotransactionPurchase 
                documentId={docId}
                onPurchaseSuccess={() => {
                  refreshCitations();
                  setShowAutoMicrotransactionDialog(false);
                }}
              />
              
              <Button 
                className="w-full bg-amber-600 hover:bg-amber-700 text-white font-semibold"
                onClick={() => {
                  setShowAutoMicrotransactionDialog(false);
                  router.push('/dashboard?showSubscriptionManager=true');
                }}
              >
                <Zap className="h-4 w-4 mr-2" />
                Upgrade Plan
              </Button>
            </div>
            
            <div className="text-xs text-gray-500 text-center">
              * You can also close this dialog and continue editing your document
            </div>
          </div>
        </DialogContent>
      </Dialog>


    </div>
  )
}