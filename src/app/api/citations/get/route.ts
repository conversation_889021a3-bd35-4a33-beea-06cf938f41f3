import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';

/**
 * API endpoint for retrieving citations from the database
 * Used by Plus/Pro users to get all stored citations for a document
 */
export async function GET(req: NextRequest) {
  try {
    // Verify authentication
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];

    // Create authenticated Supabase client
    const supabaseAuth = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader,
          },
        },
      }
    );

    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();

    if (authError || !user) {
      console.error('Authentication error:', authError);
      return NextResponse.json({ error: 'Invalid authentication' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const documentId = searchParams.get('documentId');

    // Input validation
    if (!documentId || typeof documentId !== 'string') {
      return NextResponse.json({ error: 'Valid document ID is required' }, { status: 400 });
    }

    // Validate document ID format (UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(documentId)) {
      return NextResponse.json({ error: 'Invalid document ID format' }, { status: 400 });
    }

    // Verify user owns the document using authenticated client
    const { data: document, error: docError } = await supabaseAuth
      .from('documents')
      .select('user_id')
      .eq('id', documentId)
      .single();

    if (docError || !document) {
      console.error('Document not found:', docError);
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    if (document.user_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized access to document' }, { status: 403 });
    }

    // Use authenticated client to call the RPC function
    const { data: citations, error } = await supabaseAuth.rpc('get_document_citations', {
      doc_id: documentId
    });

    if (error) {
      console.error('Error retrieving citations:', error);
      return NextResponse.json({ error: 'Failed to retrieve citations' }, { status: 500 });
    }

    return NextResponse.json({ 
      citations: citations || [],
      count: citations ? citations.length : 0
    });

  } catch (error) {
    console.error('Error in citation retrieval:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
