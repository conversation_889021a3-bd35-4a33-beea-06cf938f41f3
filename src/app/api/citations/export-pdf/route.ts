
import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';
// Import amplify_outputs.json for function URLs
// 替换静态导入
const getAmplifyOutputs = async () => {
  try {
    const outputs = await import('../../../../../amplify_outputs.json');
    return outputs.default || outputs;
  } catch (error) {
    console.error('Failed to load amplify outputs:', error);
    throw error;
  }
};

// Define an interface for the expected API response for type safety.
interface PdfApiResponse {
  html_content: string;
  citation_format: string;
  message: string;
}

export async function POST(req: NextRequest) {
  try {
    // 验证用户身份和权限
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // 创建Supabase客户端用于验证用户
    const supabaseClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader,
          },
        },
      }
    );

    // 获取当前用户
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid authentication' }, { status: 401 });
    }

    // 检查用户订阅计划 - 只有Plus和Pro用户可以导出PDF
    const { data: userProfile, error: profileError } = await supabaseClient
      .from('users')
      .select('plan_type')
      .eq('id', user.id)
      .single();

    if (profileError || !userProfile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    if (userProfile.plan_type === 'free') {
      return NextResponse.json({
        error: 'PDF export is only available for Plus and Pro users. Please upgrade your plan.'
      }, { status: 403 });
    }

    const body = await req.json();
    const { documentId, citationFormat = 'mla' } = body;

    // Input validation
    if (!documentId || typeof documentId !== 'string') {
      return NextResponse.json({ error: 'Valid document ID is required' }, { status: 400 });
    }

    // Validate citation format
    if (!['mla', 'apa'].includes(citationFormat)) {
      return NextResponse.json({ error: 'Invalid citation format. Must be "mla" or "apa"' }, { status: 400 });
    }

    // Validate document ID format (UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(documentId)) {
      return NextResponse.json({ error: 'Invalid document ID format' }, { status: 400 });
    }

    // 验证文档所有权
    const { data: document, error: docError } = await supabaseClient
      .from('documents')
      .select('user_id')
      .eq('id', documentId)
      .single();

    if (docError || !document) {
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    if (document.user_id !== user.id) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const { data: citations, error: fetchError } = await supabaseClient
      .from('citations')
      .select('csl_data')
      .eq('document_id', documentId);

    if (fetchError) {
      console.error('Error fetching citations from Supabase:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch citations' }, { status: 500 });
    }

    if (!citations || citations.length === 0) {
      return NextResponse.json({ error: 'No citations found for this document' }, { status: 404 });
    }

    const cslItems = citations.map((citation: any) => citation.csl_data);

    // Prepare the payload for our dedicated PDF generator Lambda.
    const apiPayload = {
      csl_items: cslItems,
      citation_format: citationFormat,
    };

    // Get the PDF generator function URL from outputs
    const outputs = await getAmplifyOutputs();
    const pdfGeneratorUrl = (outputs.custom as any)?.pdfGeneratorUrl;
    
    if (!pdfGeneratorUrl) {
      console.error('PDF generator URL not found in amplify_outputs.json');
      return NextResponse.json({ error: 'PDF generator service not available' }, { status: 500 });
    }

    // Send the request directly to the Lambda function URL
    const response = await fetch(pdfGeneratorUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiPayload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`PDF generator API returned ${response.status}: ${errorText}`);
      return NextResponse.json({ error: 'Failed to generate PDF' }, { status: response.status });
    }

    const responseJson = await response.json() as PdfApiResponse;

    // Return the HTML content for client-side PDF generation
    if (responseJson.html_content) {
      return NextResponse.json({
        html_content: responseJson.html_content,
        citation_format: responseJson.citation_format || citationFormat
      });
    } else {
      console.error("PDF generator backend did not return valid HTML content.", responseJson);
      return NextResponse.json({ error: 'Failed to generate citations' }, { status: 500 });
    }

  } catch (error: any) {
    // Log error details for debugging but don't expose sensitive information
    console.error('Error in PDF export route:', {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });

    // Return generic error message to prevent information leakage
    return NextResponse.json({
      error: 'An error occurred while processing your request. Please try again later.'
    }, { status: 500 });
  }
}