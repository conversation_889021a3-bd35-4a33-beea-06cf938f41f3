import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';

/**
 * API endpoint for storing citations in the database
 * Handles both 'add' and 'replace' actions for Plus/Pro users
 */
export async function POST(req: NextRequest) {
  try {
    // Verify authentication
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];

    // Create authenticated Supabase client
    const supabaseAuth = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        global: {
          headers: {
            Authorization: authHeader,
          },
        },
      }
    );

    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();

    if (authError || !user) {
      console.error('Authentication error:', authError);
      return NextResponse.json({ error: 'Invalid authentication' }, { status: 401 });
    }

    const body = await req.json();
    const { documentId, userId, citations, action } = body;

    // Input validation
    if (!documentId || typeof documentId !== 'string') {
      return NextResponse.json({ error: 'Valid document ID is required' }, { status: 400 });
    }

    if (!userId || typeof userId !== 'string') {
      return NextResponse.json({ error: 'Valid user ID is required' }, { status: 400 });
    }

    if (!citations || !Array.isArray(citations)) {
      return NextResponse.json({ error: 'Citations must be an array' }, { status: 400 });
    }

    if (!action || !['add', 'replace'].includes(action)) {
      return NextResponse.json({ error: 'Action must be "add" or "replace"' }, { status: 400 });
    }

    // Validate document ID format (UUID)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(documentId) || !uuidRegex.test(userId)) {
      return NextResponse.json({ error: 'Invalid ID format' }, { status: 400 });
    }

    // Verify user owns the document
    if (userId !== user.id) {
      return NextResponse.json({ error: 'User ID mismatch' }, { status: 403 });
    }

    // Verify user owns the document using authenticated client
    const { data: document, error: docError } = await supabaseAuth
      .from('documents')
      .select('user_id')
      .eq('id', documentId)
      .single();

    if (docError || !document) {
      console.error('Document not found:', docError);
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    if (document.user_id !== user.id) {
      return NextResponse.json({ error: 'Unauthorized access to document' }, { status: 403 });
    }

    if (action === 'replace') {
      // Clear existing citations and add new ones
      await supabaseAuth.rpc('clear_document_citations', { doc_id: documentId });
    }

    // Add new citations using authenticated client
    const { data: addedCount, error } = await supabaseAuth.rpc('add_document_citations', {
      doc_id: documentId,
      user_uuid: userId,
      citations_data: citations
    });

    if (error) {
      console.error('Error storing citations:', error);
      return NextResponse.json({ error: 'Failed to store citations' }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      addedCount,
      action 
    });

  } catch (error) {
    console.error('Error in citation storage:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
