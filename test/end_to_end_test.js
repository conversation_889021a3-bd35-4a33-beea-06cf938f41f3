/**
 * 端到端测试 - 验证完整的引用生成流程
 * 模拟真实用户的使用场景
 */

import fs from 'fs';

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';

// 测试文档内容
const testContent = `This is a test research paper about artificial intelligence. 
According to recent studies, machine learning has shown significant improvements in various fields.
The research conducted by <PERSON> et al. demonstrates the effectiveness of neural networks.
As noted in "The Future of AI" by <PERSON> (2023), we are entering a new era of technological advancement.`;

async function testCitationWorkflow(plan = 'free') {
  console.log(`\n🚀 Testing citation workflow for ${plan} plan`);
  console.log(`${'='.repeat(50)}`);

  try {
    // 步骤1: 生成引用
    console.log(`\n📝 Step 1: Generating citations`);
    const citationResponse = await fetch(`${BASE_URL}/api/cite`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title: 'Test Research Paper',
        content: testContent,
        citation_format: 'mla',
        plan: plan,
        html: true,
        userId: 'test-user-id'
      })
    });

    console.log(`   Status: ${citationResponse.status}`);
    
    if (!citationResponse.ok) {
      const errorData = await citationResponse.json();
      console.log(`   ❌ Citation generation failed: ${errorData.error}`);
      return { success: false, step: 'citation_generation', error: errorData.error };
    }

    const citationData = await citationResponse.json();
    console.log(`   ✅ Generated ${citationData.citation_count || 0} citations`);
    console.log(`   📄 HTML length: ${citationData.citation_html?.length || 0} characters`);

    // 步骤2: 测试引用格式化（如果有引用数据）
    if (citationData.citations && citationData.citations.length > 0) {
      console.log(`\n🎨 Step 2: Testing citation formatting`);
      const formatResponse = await fetch(`${BASE_URL}/api/citations/format`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          citations: citationData.citations,
          citation_format: 'mla',
          html: true
        })
      });

      console.log(`   Status: ${formatResponse.status}`);
      
      if (formatResponse.ok) {
        const formatData = await formatResponse.json();
        console.log(`   ✅ Citation formatting successful`);
        console.log(`   📄 Formatted HTML length: ${formatData.citation_html?.length || 0} characters`);
      } else {
        const errorData = await formatResponse.json();
        console.log(`   ⚠️  Citation formatting failed: ${errorData.error}`);
      }
    }

    // 步骤3: 测试Lambda函数直接调用
    console.log(`\n🔗 Step 3: Testing direct Lambda function call`);

    // 读取Lambda函数URL
    const amplifyOutputs = JSON.parse(fs.readFileSync('./amplify_outputs.json', 'utf8'));
    
    const lambdaResponse = await fetch(amplifyOutputs.custom.citationFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title: 'Test Research Paper',
        content: testContent,
        citation_format: 'mla',
        plan: plan,
        output_mode: 'html'
      })
    });

    console.log(`   Status: ${lambdaResponse.status}`);
    
    if (lambdaResponse.ok) {
      const lambdaData = await lambdaResponse.json();
      console.log(`   ✅ Direct Lambda call successful`);
      console.log(`   📄 Generated ${lambdaData.citation_count || 0} citations`);
    } else {
      const errorData = await lambdaResponse.json();
      console.log(`   ⚠️  Direct Lambda call failed: ${errorData.error}`);
    }

    console.log(`\n🎯 Overall workflow result: ✅ SUCCESS`);
    return { 
      success: true, 
      citationCount: citationData.citation_count,
      hasHtml: !!citationData.citation_html,
      htmlLength: citationData.citation_html?.length || 0
    };

  } catch (error) {
    console.log(`\n❌ Workflow failed with error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function runEndToEndTests() {
  console.log(`🧪 CiteAI End-to-End Test Suite`);
  console.log(`Testing against: ${BASE_URL}`);
  console.log(`${'='.repeat(60)}`);

  const plans = ['free', 'plus', 'pro'];
  const results = {};

  for (const plan of plans) {
    results[plan] = await testCitationWorkflow(plan);
  }

  // 汇总结果
  console.log(`\n${'='.repeat(60)}`);
  console.log(`📊 END-TO-END TEST RESULTS SUMMARY`);
  console.log(`${'='.repeat(60)}`);

  for (const [plan, result] of Object.entries(results)) {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`\n${plan.toUpperCase()} Plan: ${status}`);
    
    if (result.success) {
      console.log(`   📄 Citations generated: ${result.citationCount || 0}`);
      console.log(`   📝 HTML output: ${result.hasHtml ? 'Yes' : 'No'}`);
      console.log(`   📏 HTML length: ${result.htmlLength || 0} characters`);
    } else {
      console.log(`   ❌ Error: ${result.error || 'Unknown error'}`);
      if (result.step) {
        console.log(`   🔍 Failed at step: ${result.step}`);
      }
    }
  }

  const successfulPlans = Object.values(results).filter(r => r.success).length;
  const totalPlans = Object.keys(results).length;
  
  console.log(`\n🎯 Overall Result: ${successfulPlans}/${totalPlans} plans working correctly`);
  
  if (successfulPlans === totalPlans) {
    console.log(`🎉 ALL TESTS PASSED - CiteAI is working correctly!`);
  } else {
    console.log(`⚠️  Some issues detected - check individual plan results above`);
  }

  return results;
}

// 运行测试
runEndToEndTests().catch(console.error);
