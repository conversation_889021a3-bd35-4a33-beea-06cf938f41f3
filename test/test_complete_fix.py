#!/usr/bin/env python3

import os
import sys
import json

# Add the function directory to path
sys.path.append('amplify/functions/call-function')

# Set environment variable
os.environ['PERPLEXITY_API_KEY'] = '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'

def test_complete_citation_flow():
    """Test the complete citation generation flow"""
    
    print("🔧 Testing COMPLETE citation generation flow...")
    
    # Test data that was causing the 400 error
    test_data = {
        'title': 'Gender equality',
        'content': 'Moving on to the speech Emma delivered to launch the campaign back in September (https://www.youtube.com/watch?v=gkjW9PZBRfk). There were many layers of this speech that effectively highlight the campaign, which set it up to go viral once it was launched. The video from the United Nations YouTube channel has over 3 million views and counting.',
        'plan': 'free',
        'citation_format': 'mla'
    }
    
    try:
        # Import the main handler
        from index import lambda_handler
        
        # Create a test event
        event = {
            'body': json.dumps(test_data)
        }
        
        print(f"📝 Input data: {test_data}")
        print(f"🚀 Calling lambda_handler...")
        
        # Call the handler
        result = lambda_handler(event, None)
        
        print(f"✅ Lambda handler completed successfully!")
        print(f"📊 Status code: {result.get('statusCode')}")
        
        if result.get('body'):
            body = json.loads(result['body'])
            print(f"📊 Response body keys: {list(body.keys())}")
            
            if 'sources' in body:
                sources = body['sources']
                print(f"📊 Number of sources: {len(sources)}")
                
                for i, source in enumerate(sources[:3]):  # Show first 3
                    print(f"📚 Source {i+1}:")
                    print(f"   Title: {source.get('title', 'N/A')}")
                    print(f"   URL: {source.get('url', 'N/A')}")
                    print(f"   Is Paper: {source.get('is_paper', 'N/A')}")
            
            if 'error' in body:
                print(f"❌ Error in response: {body['error']}")
                return False
        
        print(f"🎉 COMPLETE TEST SUCCESSFUL! 400 error is FIXED!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_complete_citation_flow()
    sys.exit(0 if success else 1)
