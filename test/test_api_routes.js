/**
 * 测试API路由功能的脚本
 * 用于验证所有API端点是否正常工作
 */

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';

// 测试数据
const testData = {
  documentId: '123e4567-e89b-12d3-a456-426614174000',
  userId: '123e4567-e89b-12d3-a456-426614174001',
  citations: [
    {
      title: 'Test Article',
      author: 'Test Author',
      year: '2023',
      url: 'https://example.com'
    }
  ]
};

// 模拟认证token
const mockAuthToken = 'Bearer mock-token-for-testing';

async function testApiRoute(endpoint, method = 'POST', data = {}, headers = {}) {
  try {
    console.log(`\n🧪 Testing ${method} ${endpoint}`);
    
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      ...(method !== 'GET' && { body: JSON.stringify(data) })
    });

    const responseData = await response.text();
    let parsedData;
    
    try {
      parsedData = JSON.parse(responseData);
    } catch {
      parsedData = responseData;
    }

    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${JSON.stringify(parsedData, null, 2)}`);
    
    return {
      status: response.status,
      data: parsedData,
      success: response.status < 500 // 认为非5xx错误都是"成功"的（包括认证错误）
    };
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    return {
      status: 0,
      data: { error: error.message },
      success: false
    };
  }
}

async function runTests() {
  console.log(`🚀 Testing API routes at: ${BASE_URL}`);
  console.log('=' * 50);

  const tests = [
    // 基本连通性测试
    {
      name: 'Citations Store API',
      endpoint: '/api/citations/store',
      method: 'POST',
      data: testData,
      headers: { 'Authorization': mockAuthToken }
    },
    {
      name: 'Citations Export PDF API',
      endpoint: '/api/citations/export-pdf',
      method: 'POST',
      data: { documentId: testData.documentId, citationFormat: 'mla' },
      headers: { 'Authorization': mockAuthToken }
    },
    {
      name: 'Documents Process PDF API',
      endpoint: '/api/documents/process-pdf',
      method: 'POST',
      data: { test: 'ping' },
      headers: { 'Authorization': mockAuthToken }
    },
    {
      name: 'Citations Get API',
      endpoint: `/api/citations/get?documentId=${testData.documentId}`,
      method: 'GET',
      headers: { 'Authorization': mockAuthToken }
    },
    {
      name: 'Citations Format API',
      endpoint: '/api/citations/format',
      method: 'POST',
      data: { citations: testData.citations, citation_format: 'mla', html: true }
    },
    {
      name: 'Cite API',
      endpoint: '/api/cite',
      method: 'POST',
      data: { title: 'Test', content: 'Test content', citation_format: 'mla' }
    }
  ];

  const results = [];
  
  for (const test of tests) {
    const result = await testApiRoute(
      test.endpoint,
      test.method,
      test.data,
      test.headers
    );
    
    results.push({
      name: test.name,
      ...result
    });
  }

  // 汇总结果
  console.log('\n📊 Test Results Summary:');
  console.log('=' * 50);
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (failed.length > 0) {
    console.log('\n❌ Failed tests:');
    failed.forEach(test => {
      console.log(`   - ${test.name}: Status ${test.status}`);
    });
  }
  
  if (successful.length > 0) {
    console.log('\n✅ Successful tests:');
    successful.forEach(test => {
      console.log(`   - ${test.name}: Status ${test.status}`);
    });
  }

  return results;
}

// 运行测试
runTests().catch(console.error);
