/**
 * 简化的PDF功能测试
 * 直接测试API端点的权限验证逻辑
 */

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3001';

/**
 * 测试PDF导入权限验证
 */
async function testPdfImportPermissions() {
  console.log('🧪 测试PDF导入权限验证');
  console.log('='.repeat(40));
  
  // 测试无认证的请求
  console.log('📤 测试无认证请求...');
  try {
    const formData = new FormData();
    formData.append('pdf', new Blob(['test'], { type: 'application/pdf' }), 'test.pdf');
    formData.append('documentId', 'test-doc-id');
    
    const response = await fetch(`${API_BASE}/api/documents/process-pdf`, {
      method: 'POST',
      body: formData
    });
    
    console.log('响应状态:', response.status);
    const data = await response.json();
    console.log('响应内容:', data);
    
    if (response.status === 401) {
      console.log('✅ 正确拒绝无认证请求');
    } else {
      console.log('❌ 应该拒绝无认证请求');
    }
    
  } catch (error) {
    console.error('❌ 测试异常:', error.message);
  }
}

/**
 * 测试PDF导出权限验证
 */
async function testPdfExportPermissions() {
  console.log('\n🧪 测试PDF导出权限验证');
  console.log('='.repeat(40));
  
  // 测试无认证的请求
  console.log('📤 测试无认证请求...');
  try {
    const response = await fetch(`${API_BASE}/api/citations/export-pdf`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        documentId: 'test-doc-id',
        citationFormat: 'mla'
      })
    });
    
    console.log('响应状态:', response.status);
    const data = await response.json();
    console.log('响应内容:', data);
    
    if (response.status === 401) {
      console.log('✅ 正确拒绝无认证请求');
    } else {
      console.log('❌ 应该拒绝无认证请求');
    }
    
  } catch (error) {
    console.error('❌ 测试异常:', error.message);
  }
}

/**
 * 测试API端点可访问性
 */
async function testApiEndpoints() {
  console.log('\n🧪 测试API端点可访问性');
  console.log('='.repeat(40));
  
  const endpoints = [
    '/api/documents/process-pdf',
    '/api/citations/export-pdf',
    '/api/citations/format'
  ];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`📤 测试端点: ${endpoint}`);
      
      const response = await fetch(`${API_BASE}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
      });
      
      console.log(`📥 响应状态: ${response.status}`);
      
      if (response.status === 404) {
        console.log('❌ 端点不存在');
      } else if (response.status === 401 || response.status === 400) {
        console.log('✅ 端点存在且正确验证请求');
      } else {
        console.log('⚠️  端点存在但响应异常');
      }
      
    } catch (error) {
      console.error(`❌ 端点 ${endpoint} 测试异常:`, error.message);
    }
  }
}

/**
 * 测试开发服务器状态
 */
async function testServerStatus() {
  console.log('🧪 测试开发服务器状态');
  console.log('='.repeat(40));
  
  try {
    const response = await fetch(`${API_BASE}/api/health`, {
      method: 'GET'
    });
    
    if (response.status === 404) {
      console.log('⚠️  健康检查端点不存在，尝试访问主页...');
      
      const homeResponse = await fetch(`${API_BASE}/`, {
        method: 'GET'
      });
      
      if (homeResponse.ok) {
        console.log('✅ 开发服务器运行正常');
      } else {
        console.log('❌ 开发服务器可能有问题');
      }
    } else {
      console.log('✅ 健康检查端点响应正常');
    }
    
  } catch (error) {
    console.error('❌ 无法连接到开发服务器:', error.message);
    console.log('请确保开发服务器在 http://localhost:3001 上运行');
  }
}

/**
 * 主测试函数
 */
async function runSimpleTests() {
  console.log('🚀 开始PDF功能简化测试');
  console.log('测试目标: 验证API端点的基本功能和权限验证');
  console.log('='.repeat(60));
  
  await testServerStatus();
  await testApiEndpoints();
  await testPdfImportPermissions();
  await testPdfExportPermissions();
  
  console.log('\n📊 测试完成');
  console.log('='.repeat(30));
  console.log('如果所有端点都正确响应401/400状态码，说明权限验证正常工作');
  console.log('如果看到404错误，说明端点路径可能有问题');
}

// 运行测试
runSimpleTests().catch(console.error);
