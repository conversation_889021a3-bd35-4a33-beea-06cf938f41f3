/**
 * 集成测试脚本 - 测试CiteAI的完整工作流程
 * 模拟Free、Plus、Pro用户的不同使用场景
 */

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';

// 测试场景配置
const testScenarios = {
  free: {
    plan: 'free',
    features: ['cite', 'format'],
    restrictions: ['no_pdf_export', 'no_pdf_upload', 'limited_citations']
  },
  plus: {
    plan: 'plus',
    features: ['cite', 'format', 'store', 'export_pdf'],
    restrictions: ['no_pdf_upload', 'limited_citations']
  },
  pro: {
    plan: 'pro',
    features: ['cite', 'format', 'store', 'export_pdf', 'process_pdf'],
    restrictions: []
  }
};

// 测试数据
const testDocument = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  title: 'Test Research Paper',
  content: `This is a test research paper that discusses various topics.
According to <PERSON> (2023), artificial intelligence has revolutionized many industries.
The study by <PERSON> et al. (2022) shows significant improvements in machine learning algorithms.
As noted in "The Future of AI" by <PERSON> (2024), we are entering a new era of technological advancement.`
};

const mockCitations = [
  {
    title: 'Artificial Intelligence Revolution',
    author: 'Smith, <PERSON>',
    year: '2023',
    url: 'https://example.com/ai-revolution',
    type: 'article'
  },
  {
    title: 'Machine Learning Advances',
    author: 'Johnson, Mary; Davis, <PERSON>; <PERSON>, <PERSON>',
    year: '2022',
    url: 'https://example.com/ml-advances',
    type: 'article'
  }
];

// 模拟认证token（在真实环境中应该通过登录获取）
const mockAuthTokens = {
  free: 'Bearer mock-free-user-token',
  plus: 'Bearer mock-plus-user-token',
  pro: 'Bearer mock-pro-user-token'
};

async function makeRequest(endpoint, options = {}) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    const data = await response.text();
    let parsedData;

    try {
      parsedData = JSON.parse(data);
    } catch {
      parsedData = { raw: data };
    }

    return {
      status: response.status,
      data: parsedData,
      success: response.ok
    };
  } catch (error) {
    return {
      status: 0,
      data: { error: error.message },
      success: false
    };
  }
}

async function testCitationGeneration(plan) {
  console.log(`\n🧪 Testing citation generation for ${plan} plan`);

  const result = await makeRequest('/api/cite', {
    method: 'POST',
    body: JSON.stringify({
      title: testDocument.title,
      content: testDocument.content,
      citation_format: 'mla',
      plan: plan,
      html: true,
      userId: 'test-user-id'
    })
  });

  console.log(`   Status: ${result.status}`);
  if (result.success) {
    console.log(`   ✅ Citation generation successful`);
    console.log(`   📄 Generated ${result.data.citation_count || 0} citations`);
    return result.data;
  } else {
    console.log(`   ❌ Citation generation failed: ${result.data.error || 'Unknown error'}`);
    return null;
  }
}

async function testCitationStorage(plan, citations) {
  if (!testScenarios[plan].features.includes('store')) {
    console.log(`\n⏭️  Skipping citation storage for ${plan} plan (not supported)`);
    return true;
  }

  console.log(`\n🧪 Testing citation storage for ${plan} plan`);

  const result = await makeRequest('/api/citations/store', {
    method: 'POST',
    headers: {
      'Authorization': mockAuthTokens[plan]
    },
    body: JSON.stringify({
      documentId: testDocument.id,
      userId: 'test-user-id',
      citations: citations || mockCitations,
      action: 'replace'
    })
  });

  console.log(`   Status: ${result.status}`);
  if (result.success) {
    console.log(`   ✅ Citation storage successful`);
    return true;
  } else {
    console.log(`   ❌ Citation storage failed: ${result.data.error || 'Unknown error'}`);
    return false;
  }
}

async function testPdfExport(plan) {
  if (!testScenarios[plan].features.includes('export_pdf')) {
    console.log(`\n⏭️  Skipping PDF export for ${plan} plan (not supported)`);
    return true;
  }

  console.log(`\n🧪 Testing PDF export for ${plan} plan`);

  const result = await makeRequest('/api/citations/export-pdf', {
    method: 'POST',
    headers: {
      'Authorization': mockAuthTokens[plan]
    },
    body: JSON.stringify({
      documentId: testDocument.id,
      citationFormat: 'mla'
    })
  });

  console.log(`   Status: ${result.status}`);
  if (result.success) {
    console.log(`   ✅ PDF export successful`);
    return true;
  } else {
    console.log(`   ❌ PDF export failed: ${result.data.error || 'Unknown error'}`);
    return false;
  }
}

async function testPdfProcessing(plan) {
  if (!testScenarios[plan].features.includes('process_pdf')) {
    console.log(`\n⏭️  Skipping PDF processing for ${plan} plan (not supported)`);
    return true;
  }

  console.log(`\n🧪 Testing PDF processing for ${plan} plan`);

  // 模拟PDF文件数据（简化测试）
  const result = await makeRequest('/api/documents/process-pdf', {
    method: 'POST',
    headers: {
      'Authorization': mockAuthTokens[plan]
    },
    body: JSON.stringify({
      test: 'pdf_processing_test'
    })
  });

  console.log(`   Status: ${result.status}`);
  if (result.success) {
    console.log(`   ✅ PDF processing successful`);
    return true;
  } else {
    console.log(`   ❌ PDF processing failed: ${result.data.error || 'Unknown error'}`);
    return false;
  }
}

async function runIntegrationTest(plan) {
  console.log(`\n${'='.repeat(50)}`);
  console.log(`🚀 Running integration test for ${plan.toUpperCase()} plan`);
  console.log(`${'='.repeat(50)}`);

  const results = {
    plan,
    tests: {},
    overall: true
  };

  // 1. 测试引用生成
  const citationData = await testCitationGeneration(plan);
  results.tests.citation_generation = !!citationData;
  results.overall = results.overall && results.tests.citation_generation;

  // 2. 测试引用存储
  const storageResult = await testCitationStorage(plan, citationData?.citations);
  results.tests.citation_storage = storageResult;
  results.overall = results.overall && storageResult;

  // 3. 测试PDF导出
  const exportResult = await testPdfExport(plan);
  results.tests.pdf_export = exportResult;
  results.overall = results.overall && exportResult;

  // 4. 测试PDF处理
  const processingResult = await testPdfProcessing(plan);
  results.tests.pdf_processing = processingResult;
  results.overall = results.overall && processingResult;

  return results;
}

async function runAllTests() {
  console.log(`🧪 CiteAI Integration Test Suite`);
  console.log(`Testing against: ${BASE_URL}`);

  const allResults = {};

  // 测试所有计划
  for (const plan of ['free', 'plus', 'pro']) {
    allResults[plan] = await runIntegrationTest(plan);
  }

  // 汇总结果
  console.log(`\n${'='.repeat(60)}`);
  console.log(`📊 INTEGRATION TEST RESULTS SUMMARY`);
  console.log(`${'='.repeat(60)}`);

  for (const [plan, results] of Object.entries(allResults)) {
    const status = results.overall ? '✅ PASS' : '❌ FAIL';
    console.log(`\n${plan.toUpperCase()} Plan: ${status}`);

    for (const [test, result] of Object.entries(results.tests)) {
      const testStatus = result ? '✅' : '❌';
      console.log(`  ${testStatus} ${test.replace(/_/g, ' ')}`);
    }
  }

  const overallSuccess = Object.values(allResults).every(r => r.overall);
  console.log(`\n🎯 Overall Result: ${overallSuccess ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

  return allResults;
}

// 运行测试
runAllTests().catch(console.error);