/**
 * API功能测试 - 验证所有API端点的基本功能
 * 重点测试API路由是否正确响应，而不是完整的业务逻辑
 */

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';

async function testApiEndpoint(name, endpoint, method = 'POST', data = {}, expectedStatuses = [400, 401, 403]) {
  try {
    console.log(`\n🧪 Testing ${name}`);
    console.log(`   Endpoint: ${method} ${endpoint}`);
    
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      ...(method !== 'GET' && { body: JSON.stringify(data) })
    });

    const responseData = await response.text();
    let parsedData;
    
    try {
      parsedData = JSON.parse(responseData);
    } catch {
      parsedData = { raw: responseData };
    }

    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${JSON.stringify(parsedData, null, 2).substring(0, 200)}...`);
    
    // 检查是否是预期的状态码（表示API路由存在且正常处理请求）
    const isExpectedStatus = expectedStatuses.includes(response.status);
    const isServerError = response.status >= 500;
    
    if (isExpectedStatus && !isServerError) {
      console.log(`   ✅ API endpoint working correctly (expected status: ${response.status})`);
      return { success: true, status: response.status, data: parsedData };
    } else if (isServerError) {
      console.log(`   ❌ Server error: ${response.status}`);
      return { success: false, status: response.status, data: parsedData };
    } else {
      console.log(`   ⚠️  Unexpected status: ${response.status} (expected: ${expectedStatuses.join(', ')})`);
      return { success: true, status: response.status, data: parsedData };
    }
  } catch (error) {
    console.log(`   ❌ Request failed: ${error.message}`);
    return { success: false, status: 0, data: { error: error.message } };
  }
}

async function runApiTests() {
  console.log(`🚀 CiteAI API Functionality Test Suite`);
  console.log(`Testing against: ${BASE_URL}`);
  console.log(`${'='.repeat(60)}`);

  const tests = [
    {
      name: 'Citation Generation API',
      endpoint: '/api/cite',
      method: 'POST',
      data: { title: 'Test', content: 'Test content' },
      expectedStatuses: [400, 401, 403] // 缺少必需字段或认证
    },
    {
      name: 'Citation Storage API',
      endpoint: '/api/citations/store',
      method: 'POST',
      data: { test: 'data' },
      expectedStatuses: [400, 401] // 认证错误或缺少字段
    },
    {
      name: 'Citation Retrieval API',
      endpoint: '/api/citations/get?documentId=test-id',
      method: 'GET',
      expectedStatuses: [400, 401] // 认证错误
    },
    {
      name: 'Citation Format API',
      endpoint: '/api/citations/format',
      method: 'POST',
      data: { citations: [], citation_format: 'mla' },
      expectedStatuses: [400, 401] // 可能需要认证或更多数据
    },
    {
      name: 'PDF Export API',
      endpoint: '/api/citations/export-pdf',
      method: 'POST',
      data: { documentId: 'test-id', citationFormat: 'mla' },
      expectedStatuses: [400, 401] // 认证错误
    },
    {
      name: 'PDF Processing API',
      endpoint: '/api/documents/process-pdf',
      method: 'POST',
      data: { test: 'data' },
      expectedStatuses: [400, 401] // 认证错误或缺少文件
    }
  ];

  const results = [];
  
  for (const test of tests) {
    const result = await testApiEndpoint(
      test.name,
      test.endpoint,
      test.method,
      test.data,
      test.expectedStatuses
    );
    
    results.push({
      name: test.name,
      ...result
    });
  }

  // 汇总结果
  console.log(`\n${'='.repeat(60)}`);
  console.log(`📊 API FUNCTIONALITY TEST RESULTS`);
  console.log(`${'='.repeat(60)}`);
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`\n✅ Working APIs: ${successful.length}/${results.length}`);
  successful.forEach(test => {
    console.log(`   ✅ ${test.name}: Status ${test.status}`);
  });
  
  if (failed.length > 0) {
    console.log(`\n❌ Failed APIs: ${failed.length}/${results.length}`);
    failed.forEach(test => {
      console.log(`   ❌ ${test.name}: Status ${test.status}`);
    });
  }

  const overallSuccess = failed.length === 0;
  console.log(`\n🎯 Overall Result: ${overallSuccess ? '✅ ALL APIs WORKING' : '❌ SOME APIs FAILED'}`);
  
  return {
    total: results.length,
    successful: successful.length,
    failed: failed.length,
    overallSuccess,
    results
  };
}

// 运行测试
runApiTests().catch(console.error);
