/**
 * PDF功能测试脚本
 * 测试PDF导入和导出功能的修复效果
 * 
 * 修复内容：
 * 1. PDF导入权限：从仅Pro用户改为Plus和Pro用户
 * 2. PDF导出UI：从新窗口改为当前页面模态框
 */

import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// 配置
const API_BASE = 'http://localhost:3001';
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// 测试用户信息
const testUsers = {
  plus: {
    email: '<EMAIL>',
    password: 'testpassword123'
  },
  pro: {
    email: '<EMAIL>', 
    password: 'testpassword123'
  },
  free: {
    email: '<EMAIL>',
    password: 'testpassword123'
  }
};

/**
 * 创建或登录测试用户
 */
async function createOrLoginUser(planType) {
  console.log(`👤 创建/登录${planType}用户...`);
  
  const userInfo = testUsers[planType];
  
  try {
    // 尝试登录现有用户
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: userInfo.email,
      password: userInfo.password
    });
    
    if (!loginError && loginData.user && loginData.session) {
      console.log(`✅ ${planType}用户已存在，使用现有用户`);
      return loginData;
    }
    
    // 创建新用户
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: userInfo.email,
      password: userInfo.password
    });
    
    if (signUpError) {
      console.error(`❌ 创建${planType}用户失败:`, signUpError);
      return null;
    }
    
    if (!signUpData.session) {
      console.error(`❌ ${planType}用户创建成功但没有session`);
      return null;
    }
    
    console.log(`✅ ${planType}用户创建成功`);
    
    // 等待用户创建完成
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 更新用户订阅计划
    const { error: updateError } = await supabase
      .from('users')
      .update({ 
        plan_type: planType,
        subscription_tier: planType 
      })
      .eq('id', signUpData.user.id);
    
    if (updateError) {
      console.error(`❌ 更新${planType}用户计划失败:`, updateError);
    } else {
      console.log(`✅ ${planType}用户计划设置成功`);
    }
    
    return signUpData;
    
  } catch (error) {
    console.error(`❌ ${planType}用户创建/登录异常:`, error);
    return null;
  }
}

/**
 * 创建测试文档
 */
async function createTestDocument(authToken, userId, planType) {
  console.log(`📄 创建${planType}用户测试文档...`);
  
  try {
    const response = await fetch(`${API_BASE}/api/documents`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        title: `PDF Test Document - ${planType}`,
        content: 'This is a test document for PDF functionality testing.'
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error(`❌ ${planType}用户文档创建失败:`, errorData);
      return null;
    }
    
    const data = await response.json();
    console.log(`✅ ${planType}用户测试文档创建成功:`, data.id);
    
    return data;
    
  } catch (error) {
    console.error(`❌ ${planType}用户文档创建异常:`, error);
    return null;
  }
}

/**
 * 测试PDF导入权限
 */
async function testPdfImportPermissions(authToken, documentId, planType) {
  console.log(`📤 测试${planType}用户PDF导入权限...`);
  
  try {
    // 创建一个简单的测试PDF文件（实际上是文本文件，但用于测试权限）
    const testContent = 'This is a test PDF content for permission testing.';
    const testBlob = new Blob([testContent], { type: 'application/pdf' });
    
    const formData = new FormData();
    formData.append('pdf', testBlob, 'test.pdf');
    formData.append('documentId', documentId);
    
    const response = await fetch(`${API_BASE}/api/documents/process-pdf`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`
      },
      body: formData
    });
    
    console.log(`📥 ${planType}用户PDF导入响应状态:`, response.status);
    
    const data = await response.json();
    
    if (response.status === 403) {
      console.log(`🚫 ${planType}用户PDF导入被拒绝:`, data.error);
      return { success: false, reason: 'permission_denied', error: data.error };
    } else if (response.status === 200) {
      console.log(`✅ ${planType}用户PDF导入权限验证通过`);
      return { success: true, data };
    } else {
      console.log(`⚠️  ${planType}用户PDF导入其他错误 (${response.status}):`, data);
      return { success: false, reason: 'other_error', error: data, status: response.status };
    }
    
  } catch (error) {
    console.error(`❌ ${planType}用户PDF导入测试异常:`, error);
    return { success: false, reason: 'exception', error: error.message };
  }
}

/**
 * 测试PDF导出功能
 */
async function testPdfExport(authToken, documentId, planType) {
  console.log(`📥 测试${planType}用户PDF导出功能...`);
  
  try {
    const response = await fetch(`${API_BASE}/api/citations/export-pdf`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        documentId: documentId,
        citationFormat: 'mla'
      })
    });
    
    console.log(`📥 ${planType}用户PDF导出响应状态:`, response.status);
    
    const data = await response.json();
    
    if (response.status === 403) {
      console.log(`🚫 ${planType}用户PDF导出被拒绝:`, data.error);
      return { success: false, reason: 'permission_denied', error: data.error };
    } else if (response.status === 404) {
      console.log(`📭 ${planType}用户PDF导出无引用数据:`, data.error);
      return { success: false, reason: 'no_citations', error: data.error };
    } else if (response.status === 200) {
      console.log(`✅ ${planType}用户PDF导出成功`);
      console.log('导出结果:', {
        hasHtmlContent: !!data.html_content,
        htmlLength: data.html_content?.length || 0,
        citationFormat: data.citation_format
      });
      return { success: true, data };
    } else {
      console.log(`⚠️  ${planType}用户PDF导出其他错误 (${response.status}):`, data);
      return { success: false, reason: 'other_error', error: data, status: response.status };
    }
    
  } catch (error) {
    console.error(`❌ ${planType}用户PDF导出测试异常:`, error);
    return { success: false, reason: 'exception', error: error.message };
  }
}

/**
 * 测试单个用户的PDF功能
 */
async function testUserPdfFeatures(planType) {
  console.log(`\n🧪 开始测试${planType.toUpperCase()}用户PDF功能`);
  console.log('='.repeat(50));
  
  // 1. 创建/登录用户
  const authData = await createOrLoginUser(planType);
  if (!authData || !authData.session) {
    console.error(`❌ ${planType}用户创建/登录失败`);
    return false;
  }
  
  const authToken = authData.session.access_token;
  const userId = authData.user.id;
  
  // 2. 创建测试文档
  const document = await createTestDocument(authToken, userId, planType);
  if (!document) {
    console.error(`❌ ${planType}用户文档创建失败`);
    return false;
  }
  
  // 3. 测试PDF导入权限
  const importResult = await testPdfImportPermissions(authToken, document.id, planType);
  
  // 4. 测试PDF导出功能
  const exportResult = await testPdfExport(authToken, document.id, planType);
  
  // 5. 分析结果
  console.log(`\n📊 ${planType.toUpperCase()}用户PDF功能测试结果:`);
  console.log('PDF导入权限:', importResult.success ? '✅ 通过' : `❌ 失败 (${importResult.reason})`);
  console.log('PDF导出功能:', exportResult.success ? '✅ 通过' : `❌ 失败 (${exportResult.reason})`);
  
  // 根据计划类型验证预期结果
  const expectedImportAccess = planType === 'plus' || planType === 'pro';
  const expectedExportAccess = planType === 'plus' || planType === 'pro';
  
  const importCorrect = expectedImportAccess ? importResult.success : (importResult.reason === 'permission_denied');
  const exportCorrect = expectedExportAccess ? 
    (exportResult.success || exportResult.reason === 'no_citations') : 
    (exportResult.reason === 'permission_denied');
  
  console.log('权限验证结果:', importCorrect && exportCorrect ? '✅ 正确' : '❌ 错误');
  
  return importCorrect && exportCorrect;
}

/**
 * 主测试函数
 */
async function runPdfTests() {
  console.log('🚀 开始PDF功能修复测试');
  console.log('测试目标: 验证PDF导入和导出功能的权限和UI修复');
  console.log('修复内容: PDF导入支持Plus+Pro用户，PDF导出使用模态框');
  console.log('='.repeat(60));
  
  const testPlans = ['free', 'plus', 'pro'];
  const results = {};
  
  for (const planType of testPlans) {
    try {
      results[planType] = await testUserPdfFeatures(planType);
    } catch (error) {
      console.error(`❌ ${planType}用户测试异常:`, error);
      results[planType] = false;
    }
  }
  
  // 输出最终结果
  console.log('\n📊 PDF功能测试结果汇总');
  console.log('='.repeat(30));
  
  for (const [planType, success] of Object.entries(results)) {
    const status = success ? '✅ 通过' : '❌ 失败';
    console.log(`${planType.toUpperCase()}用户: ${status}`);
  }
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('\n🎉 所有PDF功能测试通过！修复成功！');
  } else {
    console.log('\n⚠️  部分测试失败，需要进一步检查');
  }
  
  return results;
}

// 运行测试
runPdfTests().catch(console.error);
