/**
 * 测试真实用户session的API功能
 */

import { createClient } from '@supabase/supabase-js';

// Supabase配置
const supabaseUrl = 'https://sfpvfogiecwnborsdijj.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNmcHZmb2dpZWN3bmJvcnNkaWpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTUxODMsImV4cCI6MjA2NzkzMTE4M30.3CciuX7cK7Q3e9aYWeU5jC1ZOrVnMN_qL7hmRm6SPHM';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNmcHZmb2dpZWN3bmJvcnNkaWpqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjM1NTE4MywiZXhwIjoyMDY3OTMxMTgzfQ.yRqzQQPqpv4kT6l2G0CsY8sNHaEeil8ASN1cv04M0cg';

const supabase = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function loginTestUser() {
  console.log('🔐 登录测试用户...');
  
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    
    if (error) {
      console.error('❌ 登录失败:', error);
      return null;
    }
    
    console.log('✅ 登录成功');
    console.log('用户ID:', data.user.id);
    console.log('Access Token:', data.session.access_token.substring(0, 50) + '...');
    
    return data.session.access_token;
  } catch (error) {
    console.error('❌ 登录异常:', error);
    return null;
  }
}

async function testApiWithRealToken(token, userId, documentId) {
  console.log('🧪 使用真实token测试API...');
  
  const baseUrl = 'http://localhost:3000';
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  };
  
  // 测试citation store API
  console.log('📝 测试 /api/citations/store...');
  try {
    const storeResponse = await fetch(`${baseUrl}/api/citations/store`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        documentId,
        userId,
        citations: [
          {
            title: 'Test Citation',
            author: 'Test Author',
            year: '2024',
            type: 'article'
          }
        ],
        action: 'add'
      })
    });
    
    const storeResult = await storeResponse.text();
    console.log(`Citation Store API 响应 (${storeResponse.status}):`, storeResult);
    
    if (storeResponse.ok) {
      console.log('✅ Citation store 测试成功');
    } else {
      console.log('❌ Citation store 测试失败');
    }
  } catch (error) {
    console.error('❌ Citation Store API 测试异常:', error);
  }
  
  console.log('\n');
  
  // 测试PDF export API
  console.log('📄 测试 /api/citations/export-pdf...');
  try {
    const exportResponse = await fetch(`${baseUrl}/api/citations/export-pdf`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        documentId,
        citationFormat: 'mla'
      })
    });
    
    const exportResult = await exportResponse.text();
    console.log(`PDF Export API 响应 (${exportResponse.status}):`, exportResult);
    
    if (exportResponse.ok) {
      console.log('✅ PDF export 测试成功');
    } else {
      console.log('❌ PDF export 测试失败');
    }
  } catch (error) {
    console.error('❌ PDF Export API 测试异常:', error);
  }
}

async function getUserAndDocument() {
  console.log('📋 获取测试用户和文档信息...');
  
  try {
    // 获取测试用户
    const { data: users, error: usersError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();
    
    if (usersError || !users) {
      console.error('❌ 找不到测试用户:', usersError);
      return null;
    }
    
    console.log('✅ 找到测试用户:', users.id);
    
    // 获取测试文档
    const { data: documents, error: docsError } = await supabaseAdmin
      .from('documents')
      .select('*')
      .eq('user_id', users.id)
      .single();
    
    if (docsError || !documents) {
      console.error('❌ 找不到测试文档:', docsError);
      return null;
    }
    
    console.log('✅ 找到测试文档:', documents.id);
    
    return { user: users, document: documents };
  } catch (error) {
    console.error('❌ 获取用户和文档信息异常:', error);
    return null;
  }
}

async function runRealUserTest() {
  console.log('🚀 开始真实用户API测试...\n');
  
  // 1. 获取用户和文档信息
  const testData = await getUserAndDocument();
  if (!testData) {
    console.log('❌ 无法获取测试数据，停止测试');
    return;
  }
  
  console.log('\n');
  
  // 2. 登录获取token
  const token = await loginTestUser();
  if (!token) {
    console.log('❌ 无法获取认证token，停止测试');
    return;
  }
  
  console.log('\n');
  
  // 3. 测试API端点
  await testApiWithRealToken(token, testData.user.id, testData.document.id);
  
  console.log('\n✅ 真实用户API测试完成');
}

// 运行测试
runRealUserTest().catch(console.error);
