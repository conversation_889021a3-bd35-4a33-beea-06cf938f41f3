/**
 * Lambda函数连接测试
 * 直接测试Lambda函数是否可以访问
 */

import fs from 'fs';

// 从amplify_outputs.json读取Lambda函数URL
const amplifyOutputs = JSON.parse(fs.readFileSync('./amplify_outputs.json', 'utf8'));

async function testLambdaFunction(name, url, payload = {}) {
  try {
    console.log(`\n🧪 Testing ${name}`);
    console.log(`   URL: ${url.substring(0, 50)}...`);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    const data = await response.text();
    let parsedData;
    
    try {
      parsedData = JSON.parse(data);
    } catch {
      parsedData = { raw: data };
    }

    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${JSON.stringify(parsedData, null, 2).substring(0, 200)}...`);
    
    if (response.ok || response.status === 400) {
      // 200 OK 或 400 Bad Request 都表示Lambda函数正常工作
      console.log(`   ✅ Lambda function is accessible and responding`);
      return { success: true, status: response.status, data: parsedData };
    } else {
      console.log(`   ❌ Lambda function error: ${response.status}`);
      return { success: false, status: response.status, data: parsedData };
    }
  } catch (error) {
    console.log(`   ❌ Connection failed: ${error.message}`);
    return { success: false, status: 0, data: { error: error.message } };
  }
}

async function runLambdaTests() {
  console.log(`🚀 Lambda Function Connectivity Test`);
  console.log(`${'='.repeat(50)}`);

  const lambdaFunctions = [
    {
      name: 'Citation Function',
      url: amplifyOutputs.custom.citationFunctionUrl,
      payload: { test: 'ping' }
    },
    {
      name: 'PDF Generator Function',
      url: amplifyOutputs.custom.pdfGeneratorUrl,
      payload: { test: 'ping' }
    },
    {
      name: 'PDF Processor Function',
      url: amplifyOutputs.custom.pdfProcessorUrl,
      payload: { test: 'ping' }
    }
  ];

  const results = [];
  
  for (const func of lambdaFunctions) {
    const result = await testLambdaFunction(func.name, func.url, func.payload);
    results.push({
      name: func.name,
      ...result
    });
  }

  // 汇总结果
  console.log(`\n${'='.repeat(50)}`);
  console.log(`📊 LAMBDA CONNECTIVITY RESULTS`);
  console.log(`${'='.repeat(50)}`);
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`\n✅ Working Lambda Functions: ${successful.length}/${results.length}`);
  successful.forEach(test => {
    console.log(`   ✅ ${test.name}: Status ${test.status}`);
  });
  
  if (failed.length > 0) {
    console.log(`\n❌ Failed Lambda Functions: ${failed.length}/${results.length}`);
    failed.forEach(test => {
      console.log(`   ❌ ${test.name}: Status ${test.status}`);
    });
  }

  const overallSuccess = failed.length === 0;
  console.log(`\n🎯 Overall Result: ${overallSuccess ? '✅ ALL LAMBDA FUNCTIONS ACCESSIBLE' : '❌ SOME LAMBDA FUNCTIONS FAILED'}`);
  
  return {
    total: results.length,
    successful: successful.length,
    failed: failed.length,
    overallSuccess,
    results
  };
}

// 运行测试
runLambdaTests().catch(console.error);
