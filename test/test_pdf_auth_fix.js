/**
 * PDF认证修复测试脚本
 * 测试PDF导入功能的认证和权限修复
 */

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3001';

/**
 * 测试PDF导入端点的认证处理
 */
async function testPdfImportAuth() {
  console.log('🧪 测试PDF导入认证修复');
  console.log('='.repeat(50));
  
  // 测试1: 无认证头
  console.log('📤 测试1: 无认证头请求...');
  try {
    const formData = new FormData();
    formData.append('pdf', new Blob(['test pdf content'], { type: 'application/pdf' }), 'test.pdf');
    formData.append('documentId', 'test-doc-id');
    
    const response = await fetch(`${API_BASE}/api/documents/process-pdf`, {
      method: 'POST',
      body: formData
    });
    
    console.log('响应状态:', response.status);
    const data = await response.json();
    console.log('响应内容:', data);
    
    if (response.status === 401 && data.error === 'Authentication required') {
      console.log('✅ 正确拒绝无认证请求');
    } else {
      console.log('❌ 应该返回401认证错误');
    }
    
  } catch (error) {
    console.error('❌ 测试异常:', error.message);
  }
  
  // 测试2: 无效认证头
  console.log('\n📤 测试2: 无效认证头请求...');
  try {
    const formData = new FormData();
    formData.append('pdf', new Blob(['test pdf content'], { type: 'application/pdf' }), 'test.pdf');
    formData.append('documentId', 'test-doc-id');
    
    const response = await fetch(`${API_BASE}/api/documents/process-pdf`, {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer invalid-token'
      },
      body: formData
    });
    
    console.log('响应状态:', response.status);
    const data = await response.json();
    console.log('响应内容:', data);
    
    if (response.status === 401 && data.error === 'Invalid authentication') {
      console.log('✅ 正确拒绝无效认证');
    } else {
      console.log('❌ 应该返回401无效认证错误');
    }
    
  } catch (error) {
    console.error('❌ 测试异常:', error.message);
  }
  
  // 测试3: 检查错误处理
  console.log('\n📤 测试3: 检查错误处理...');
  try {
    const response = await fetch(`${API_BASE}/api/documents/process-pdf`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });
    
    console.log('响应状态:', response.status);
    const data = await response.json();
    console.log('响应内容:', data);
    
    if (response.status === 401) {
      console.log('✅ 正确处理错误请求格式');
    } else {
      console.log('⚠️  响应状态异常');
    }
    
  } catch (error) {
    console.error('❌ 测试异常:', error.message);
  }
}

/**
 * 测试前端认证逻辑模拟
 */
async function testFrontendAuthLogic() {
  console.log('\n🧪 模拟前端认证逻辑');
  console.log('='.repeat(50));
  
  // 模拟session检查逻辑
  console.log('📤 模拟session检查...');
  
  const mockSessions = [
    { name: '有效session', session: { access_token: 'valid-token', user: { id: 'user-123' } } },
    { name: '无session', session: null },
    { name: '无access_token', session: { user: { id: 'user-123' } } },
    { name: '空access_token', session: { access_token: '', user: { id: 'user-123' } } }
  ];
  
  for (const mockSession of mockSessions) {
    console.log(`\n测试场景: ${mockSession.name}`);
    
    const session = mockSession.session;
    
    // 模拟前端逻辑
    if (!session || !session.access_token) {
      console.log('❌ 前端会显示: Authentication required. Please log in again.');
      continue;
    }
    
    console.log('✅ 前端会继续发送请求，token:', session.access_token);
    
    // 模拟请求
    try {
      const formData = new FormData();
      formData.append('pdf', new Blob(['test'], { type: 'application/pdf' }), 'test.pdf');
      formData.append('documentId', 'test-doc');
      
      const response = await fetch(`${API_BASE}/api/documents/process-pdf`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        },
        body: formData
      });
      
      console.log('模拟请求响应状态:', response.status);
      
    } catch (error) {
      console.log('模拟请求失败:', error.message);
    }
  }
}

/**
 * 测试API端点可用性
 */
async function testApiAvailability() {
  console.log('\n🧪 测试API端点可用性');
  console.log('='.repeat(50));
  
  const endpoints = [
    { path: '/api/documents/process-pdf', method: 'POST' },
    { path: '/api/citations/export-pdf', method: 'POST' },
    { path: '/api/citations/format', method: 'POST' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`📤 测试端点: ${endpoint.method} ${endpoint.path}`);
      
      const response = await fetch(`${API_BASE}${endpoint.path}`, {
        method: endpoint.method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
      });
      
      console.log(`📥 响应状态: ${response.status}`);
      
      if (response.status === 404) {
        console.log('❌ 端点不存在');
      } else if (response.status === 401 || response.status === 400) {
        console.log('✅ 端点存在且正确验证请求');
      } else {
        console.log('⚠️  端点存在但响应异常');
      }
      
    } catch (error) {
      console.error(`❌ 端点 ${endpoint.path} 测试异常:`, error.message);
    }
  }
}

/**
 * 主测试函数
 */
async function runAuthFixTests() {
  console.log('🚀 开始PDF认证修复测试');
  console.log('测试目标: 验证PDF导入功能的认证和权限修复');
  console.log('修复内容: 改进session检查、错误处理和调试日志');
  console.log('='.repeat(70));
  
  await testApiAvailability();
  await testPdfImportAuth();
  await testFrontendAuthLogic();
  
  console.log('\n📊 测试完成');
  console.log('='.repeat(30));
  console.log('✅ 如果所有端点都正确响应401状态码，说明认证验证正常工作');
  console.log('✅ 前端现在会检查session有效性并提供更好的错误提示');
  console.log('✅ 后端添加了详细的调试日志帮助排查问题');
  console.log('\n🔍 下一步: 在生产环境中查看浏览器控制台的详细日志');
  console.log('   - 检查session状态');
  console.log('   - 检查认证token传递');
  console.log('   - 查看后端API日志');
}

// 运行测试
runAuthFixTests().catch(console.error);
