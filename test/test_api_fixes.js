/**
 * 测试API修复的脚本
 * 验证Plus/Pro用户的citation存储、PDF处理和PDF导出功能
 */

import { createClient } from '@supabase/supabase-js';

// Supabase配置
const supabaseUrl = 'https://sfpvfogiecwnborsdijj.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNmcHZmb2dpZWN3bmJvcnNkaWpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTUxODMsImV4cCI6MjA2NzkzMTE4M30.3CciuX7cK7Q3e9aYWeU5jC1ZOrVnMN_qL7hmRm6SPHM';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNmcHZmb2dpZWN3bmJvcnNkaWpqIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjM1NTE4MywiZXhwIjoyMDY3OTMxMTgzfQ.yRqzQQPqpv4kT6l2G0CsY8sNHaEeil8ASN1cv04M0cg';

const supabase = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function testDatabaseConnection() {
  console.log('🔍 测试数据库连接...');
  
  try {
    // 测试users表查询
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, plan_type, subscription_tier')
      .limit(5);
    
    if (usersError) {
      console.error('❌ Users表查询失败:', usersError);
      return false;
    }
    
    console.log('✅ Users表查询成功');
    console.log('用户数据样本:', users);
    
    // 检查是否有Plus/Pro用户
    const plusProUsers = users.filter(user => user.plan_type === 'plus' || user.plan_type === 'pro');
    console.log(`📊 找到 ${plusProUsers.length} 个Plus/Pro用户`);
    
    return { users, plusProUsers };
  } catch (error) {
    console.error('❌ 数据库连接测试失败:', error);
    return false;
  }
}

async function createTestUser() {
  console.log('👤 创建测试用户...');

  try {
    // 首先创建auth用户
    const testEmail = '<EMAIL>';
    const testPassword = 'testpassword123';

    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true
    });

    if (authError) {
      console.error('❌ 创建auth用户失败:', authError);
      return null;
    }

    console.log('✅ Auth用户创建成功:', authData.user.id);

    // 然后创建profile用户，使用service role
    const { data, error } = await supabaseAdmin
      .from('users')
      .upsert({
        id: authData.user.id,
        email: testEmail,
        plan_type: 'pro',
        subscription_tier: 'pro',
        first_name: 'Test',
        last_name: 'User'
      })
      .select();

    if (error) {
      console.error('❌ 创建用户profile失败:', error);
      return null;
    }

    console.log('✅ 用户profile创建成功:', data[0]);
    return data[0];
  } catch (error) {
    console.error('❌ 创建测试用户异常:', error);
    return null;
  }
}

async function createTestDocument(userId) {
  console.log('📄 创建测试文档...');

  try {
    // 使用service role创建文档
    const { data, error } = await supabaseAdmin
      .from('documents')
      .insert({
        user_id: userId,
        title: 'Test Document for API Testing',
        content: 'This is a test document for testing API functionality.',
        word_count: 10,
        status: 'completed'
      })
      .select();

    if (error) {
      console.error('❌ 创建测试文档失败:', error);
      return null;
    }

    console.log('✅ 测试文档创建成功:', data[0]);
    return data[0];
  } catch (error) {
    console.error('❌ 创建测试文档异常:', error);
    return null;
  }
}

async function testApiEndpoints(userId, documentId) {
  console.log('🧪 测试API端点...');

  const baseUrl = 'http://localhost:3000';

  // 获取真实的JWT token
  const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
    type: 'magiclink',
    email: '<EMAIL>'
  });

  if (sessionError) {
    console.error('❌ 无法生成session:', sessionError);
    return;
  }

  // 使用service role key进行API测试
  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${supabaseServiceKey}`
  };
  
  // 测试citation store API
  console.log('📝 测试 /api/citations/store...');
  try {
    const storeResponse = await fetch(`${baseUrl}/api/citations/store`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        documentId,
        userId,
        citations: [
          {
            title: 'Test Citation',
            author: 'Test Author',
            year: '2024'
          }
        ],
        action: 'add'
      })
    });
    
    const storeResult = await storeResponse.text();
    console.log(`Citation Store API 响应 (${storeResponse.status}):`, storeResult);
  } catch (error) {
    console.error('❌ Citation Store API 测试失败:', error);
  }
  
  // 测试PDF export API
  console.log('📄 测试 /api/citations/export-pdf...');
  try {
    const exportResponse = await fetch(`${baseUrl}/api/citations/export-pdf`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        documentId,
        citationFormat: 'mla'
      })
    });
    
    const exportResult = await exportResponse.text();
    console.log(`PDF Export API 响应 (${exportResponse.status}):`, exportResult);
  } catch (error) {
    console.error('❌ PDF Export API 测试失败:', error);
  }
}

async function runTests() {
  console.log('🚀 开始API修复测试...\n');
  
  // 1. 测试数据库连接
  const dbResult = await testDatabaseConnection();
  if (!dbResult) {
    console.log('❌ 数据库连接失败，停止测试');
    return;
  }
  
  console.log('\n');
  
  // 2. 创建测试用户
  const testUser = await createTestUser();
  if (!testUser) {
    console.log('❌ 无法创建测试用户，停止测试');
    return;
  }
  
  console.log('\n');
  
  // 3. 创建测试文档
  const testDocument = await createTestDocument(testUser.id);
  if (!testDocument) {
    console.log('❌ 无法创建测试文档，停止测试');
    return;
  }
  
  console.log('\n');
  
  // 4. 测试API端点
  await testApiEndpoints(testUser.id, testDocument.id);
  
  console.log('\n✅ 测试完成');
}

// 运行测试
runTests().catch(console.error);
