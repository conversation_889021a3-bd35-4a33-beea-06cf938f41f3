/**
 * 测试引用获取修复的脚本
 * 专门验证Pro/Plus用户的引用存储和获取功能
 * 
 * 修复内容：
 * 1. /api/citations/get 端点使用认证客户端
 * 2. 引用存储逻辑的调试日志
 * 3. RLS策略权限验证
 */

import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';

// Supabase配置
const supabaseUrl = 'https://sfpvfogiecwnborsdijj.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNmcHZmb2dpZWN3bmJvcnNkaWpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIzNTUxODMsImV4cCI6MjA2NzkzMTE4M30.3CciuX7cK7Q3e9aYWeU5jC1ZOrVnMN_qL7hmRm6SPHM';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// 测试用户凭据
const testUsers = {
  pro: {
    email: '<EMAIL>',
    password: 'testpassword123'
  },
  plus: {
    email: '<EMAIL>', 
    password: 'testpassword123'
  },
  free: {
    email: '<EMAIL>',
    password: 'testpassword123'
  }
};

// API基础URL
const API_BASE = 'http://localhost:3001';

/**
 * 创建测试用户并设置订阅计划
 */
async function createTestUser(planType) {
  console.log(`👤 创建${planType}测试用户...`);
  
  const userInfo = testUsers[planType];
  
  try {
    // 尝试登录现有用户
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: userInfo.email,
      password: userInfo.password
    });

    if (!loginError && loginData.user && loginData.session) {
      console.log(`✅ ${planType}用户已存在，使用现有用户`);
      return loginData;
    }
    
    // 创建新用户
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: userInfo.email,
      password: userInfo.password
    });
    
    if (signUpError) {
      console.error(`❌ 创建${planType}用户失败:`, signUpError);
      return null;
    }

    if (!signUpData.session) {
      console.error(`❌ ${planType}用户创建成功但没有session`);
      return null;
    }

    console.log(`✅ ${planType}用户创建成功`);

    // 等待用户创建完成
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 更新用户订阅计划
    const { error: updateError } = await supabase
      .from('users')
      .update({
        plan_type: planType,
        subscription_tier: planType
      })
      .eq('id', signUpData.user.id);

    if (updateError) {
      console.error(`❌ 更新${planType}用户计划失败:`, updateError);
    } else {
      console.log(`✅ ${planType}用户计划设置成功`);
    }

    return signUpData;
    
  } catch (error) {
    console.error(`❌ 创建${planType}用户异常:`, error);
    return null;
  }
}

/**
 * 创建测试文档
 */
async function createTestDocument(authToken, userId) {
  console.log('📄 创建测试文档...');
  
  try {
    const { data: document, error } = await supabase
      .from('documents')
      .insert({
        user_id: userId,
        title: 'Citation Test Document',
        content: 'This is a test document for citation functionality. It contains some text that should generate citations.',
        word_count: 20,
        status: 'completed'
      })
      .select()
      .single();
    
    if (error) {
      console.error('❌ 创建文档失败:', error);
      return null;
    }
    
    console.log('✅ 测试文档创建成功:', document.id);
    return document;
    
  } catch (error) {
    console.error('❌ 创建文档异常:', error);
    return null;
  }
}

/**
 * 测试引用生成
 */
async function testCitationGeneration(authToken, document, planType) {
  console.log(`🔗 测试${planType}用户引用生成...`);
  
  try {
    const response = await fetch(`${API_BASE}/api/cite`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        title: document.title,
        content: document.content,
        citation_format: 'mla',
        plan: planType,
        html: true,
        userId: document.user_id
      })
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      console.error(`❌ ${planType}用户引用生成失败:`, data);
      return null;
    }
    
    console.log(`✅ ${planType}用户引用生成成功`);
    console.log('引用数据:', {
      hasCitationHtml: !!data.citation_html,
      citationCount: data.citation_count,
      hasCitations: !!data.citations,
      citationsLength: data.citations?.length || 0
    });
    
    return data;
    
  } catch (error) {
    console.error(`❌ ${planType}用户引用生成异常:`, error);
    return null;
  }
}

/**
 * 测试引用存储
 */
async function testCitationStorage(authToken, documentId, userId, citationData, planType) {
  console.log(`💾 测试${planType}用户引用存储...`);

  if (!citationData.citations || citationData.citations.length === 0) {
    console.log(`⚠️  ${planType}用户没有引用数据需要存储`);
    return { success: true, reason: 'no_citations' };
  }

  try {
    const response = await fetch(`${API_BASE}/api/citations/store`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        documentId: documentId,
        userId: userId, // 使用正确的用户ID
        citations: citationData.citations,
        action: 'replace'
      })
    });

    const data = await response.json();

    if (!response.ok) {
      console.error(`❌ ${planType}用户引用存储失败 (${response.status}):`, data);
      return { success: false, error: data, status: response.status };
    }

    console.log(`✅ ${planType}用户引用存储成功`);
    console.log('存储结果:', data);

    return { success: true, data };

  } catch (error) {
    console.error(`❌ ${planType}用户引用存储异常:`, error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试引用格式化（新修复的功能）
 */
async function testCitationFormatting(citations, planType) {
  console.log(`🎨 测试${planType}用户引用格式化...`);

  try {
    const response = await fetch(`${API_BASE}/api/citations/format`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        citations: citations,
        citation_format: 'mla',
        html: true
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error(`❌ ${planType}用户引用格式化失败 (${response.status}):`, errorData);
      return { success: false, error: errorData, status: response.status };
    }

    const data = await response.json();
    console.log(`✅ ${planType}用户引用格式化成功`);
    console.log('格式化结果:', {
      hasCitationHtml: !!data.citation_html,
      htmlLength: data.citation_html?.length || 0,
      citationCount: data.citation_count
    });

    return { success: true, data };

  } catch (error) {
    console.error(`❌ ${planType}用户引用格式化异常:`, error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试引用获取（这是我们修复的主要功能）
 */
async function testCitationRetrieval(authToken, documentId, planType) {
  console.log(`📥 测试${planType}用户引用获取...`);
  
  try {
    const response = await fetch(`${API_BASE}/api/citations/get?documentId=${documentId}`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      console.error(`❌ ${planType}用户引用获取失败 (${response.status}):`, data);
      return { success: false, error: data, status: response.status };
    }
    
    console.log(`✅ ${planType}用户引用获取成功`);
    console.log('获取的引用数据:', {
      citationCount: data.count,
      hasCitations: !!data.citations,
      citationsLength: data.citations?.length || 0
    });
    
    return { success: true, data };
    
  } catch (error) {
    console.error(`❌ ${planType}用户引用获取异常:`, error);
    return { success: false, error: error.message };
  }
}

/**
 * 测试完整的引用工作流程
 */
async function testCitationWorkflow(planType) {
  console.log(`\n🧪 开始测试${planType.toUpperCase()}用户引用工作流程`);
  console.log('='.repeat(50));
  
  // 1. 创建/登录用户
  const authData = await createTestUser(planType);
  if (!authData || !authData.user) {
    console.error(`❌ ${planType}用户创建/登录失败`);
    return false;
  }
  
  const authToken = authData.session.access_token;
  const userId = authData.user.id;
  
  // 2. 创建测试文档
  const document = await createTestDocument(authToken, userId);
  if (!document) {
    console.error(`❌ ${planType}用户文档创建失败`);
    return false;
  }
  
  // 3. 生成引用
  const citationData = await testCitationGeneration(authToken, document, planType);
  if (!citationData) {
    console.error(`❌ ${planType}用户引用生成失败`);
    return false;
  }
  
  // 4. 对于Plus/Pro用户，测试引用存储和获取（这是修复的重点）
  if (planType === 'plus' || planType === 'pro') {
    console.log(`🔍 ${planType}用户需要测试引用存储和获取功能...`);

    // 4a. 测试引用存储
    const storageResult = await testCitationStorage(authToken, document.id, userId, citationData, planType);
    if (!storageResult.success && storageResult.reason !== 'no_citations') {
      console.error(`❌ ${planType}用户引用存储失败`);
      console.error('错误详情:', storageResult);
      return false;
    }

    // 等待一下确保引用已存储
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 4b. 测试引用获取（这是修复的重点）
    const retrievalResult = await testCitationRetrieval(authToken, document.id, planType);

    if (!retrievalResult.success) {
      console.error(`❌ ${planType}用户引用获取失败 - 这是我们修复的主要问题！`);
      console.error('错误详情:', retrievalResult);
      return false;
    }

    console.log(`✅ ${planType}用户引用获取成功 - 修复生效！`);

    // 4c. 测试引用格式化（新修复的功能）
    if (retrievalResult.data && retrievalResult.data.citations && retrievalResult.data.citations.length > 0) {
      console.log(`🎨 测试${planType}用户引用格式化...`);

      const formatResult = await testCitationFormatting(retrievalResult.data.citations, planType);
      if (!formatResult.success) {
        console.error(`❌ ${planType}用户引用格式化失败`);
        console.error('错误详情:', formatResult);
        return false;
      }

      console.log(`✅ ${planType}用户引用格式化成功`);
    }
  }
  
  // 5. 清理测试数据
  await supabase.from('documents').delete().eq('id', document.id);
  
  console.log(`✅ ${planType}用户测试完成`);
  return true;
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始引用获取修复测试');
  console.log('测试目标: 验证Pro/Plus用户的引用存储和获取功能');
  console.log('修复内容: /api/citations/get 端点认证问题');
  console.log('='.repeat(60));
  
  const results = {};
  
  // 测试所有用户类型
  for (const planType of ['free', 'plus', 'pro']) {
    try {
      results[planType] = await testCitationWorkflow(planType);
    } catch (error) {
      console.error(`❌ ${planType}用户测试异常:`, error);
      results[planType] = false;
    }
  }
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总');
  console.log('='.repeat(30));
  
  for (const [planType, success] of Object.entries(results)) {
    const status = success ? '✅ 通过' : '❌ 失败';
    console.log(`${planType.toUpperCase()}用户: ${status}`);
  }
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('\n🎉 所有测试通过！引用获取修复成功！');
  } else {
    console.log('\n⚠️  部分测试失败，需要进一步检查');
  }
  
  return results;
}

// 运行测试
runTests().catch(console.error);
