/**
 * 测试引用格式化修复的脚本
 * 专门验证 /api/citations/format 端点的修复效果
 * 
 * 修复内容：
 * 1. Lambda函数中format_only模式的参数验证逻辑
 * 2. 确保format_only模式不需要title和content字段
 */

import fetch from 'node-fetch';

// API基础URL
const API_BASE = 'http://localhost:3001';

/**
 * 测试引用格式化API
 */
async function testCitationFormatting() {
  console.log('🧪 测试引用格式化API修复');
  console.log('='.repeat(40));
  
  // 模拟从数据库获取的引用数据
  const mockCitations = [
    {
      id: 'test-1',
      csl_data: {
        type: 'webpage',
        title: 'Test Article 1',
        author: [{ family: 'Smith', given: '<PERSON>' }],
        URL: 'https://example.com/article1',
        accessed: { 'date-parts': [[2024, 1, 15]] },
        issued: { 'date-parts': [[2024, 1, 10]] }
      }
    },
    {
      id: 'test-2', 
      csl_data: {
        type: 'webpage',
        title: 'Test Article 2',
        author: [{ family: '<PERSON>', given: 'Jane' }],
        URL: 'https://example.com/article2',
        accessed: { 'date-parts': [[2024, 1, 16]] },
        issued: { 'date-parts': [[2024, 1, 12]] }
      }
    }
  ];
  
  try {
    console.log('📤 发送格式化请求...');
    console.log('引用数量:', mockCitations.length);
    
    const response = await fetch(`${API_BASE}/api/citations/format`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        citations: mockCitations,
        citation_format: 'mla',
        html: true
      })
    });
    
    console.log('📥 响应状态:', response.status);
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ 格式化失败:', errorData);
      return false;
    }
    
    const data = await response.json();
    console.log('✅ 格式化成功!');
    console.log('响应数据:', {
      hasCitationHtml: !!data.citation_html,
      htmlLength: data.citation_html?.length || 0,
      citationCount: data.citation_count,
      hasCitations: !!data.citations
    });
    
    if (data.citation_html) {
      console.log('📄 生成的HTML预览:');
      console.log(data.citation_html.substring(0, 200) + '...');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
    return false;
  }
}

/**
 * 测试错误情况 - 空引用数组
 */
async function testEmptyCitations() {
  console.log('\n🧪 测试空引用数组处理');
  console.log('='.repeat(40));
  
  try {
    const response = await fetch(`${API_BASE}/api/citations/format`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        citations: [],
        citation_format: 'mla',
        html: true
      })
    });
    
    console.log('📥 响应状态:', response.status);
    
    if (response.status === 400) {
      const errorData = await response.json();
      console.log('✅ 正确处理空引用数组:', errorData.error);
      return true;
    } else {
      console.error('❌ 未正确处理空引用数组');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
    return false;
  }
}

/**
 * 测试无效引用数据
 */
async function testInvalidCitations() {
  console.log('\n🧪 测试无效引用数据处理');
  console.log('='.repeat(40));
  
  const invalidCitations = [
    { id: 'invalid-1' }, // 缺少csl_data
    { id: 'invalid-2', csl_data: null }, // csl_data为null
    null, // 完全无效的引用
  ];
  
  try {
    const response = await fetch(`${API_BASE}/api/citations/format`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        citations: invalidCitations,
        citation_format: 'mla',
        html: true
      })
    });
    
    console.log('📥 响应状态:', response.status);
    
    if (response.status === 400) {
      const errorData = await response.json();
      console.log('✅ 正确处理无效引用数据:', errorData.error);
      return true;
    } else {
      console.error('❌ 未正确处理无效引用数据');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 测试异常:', error);
    return false;
  }
}

/**
 * 测试不同格式
 */
async function testDifferentFormats() {
  console.log('\n🧪 测试不同引用格式');
  console.log('='.repeat(40));
  
  const mockCitation = [{
    id: 'test-format',
    csl_data: {
      type: 'webpage',
      title: 'Format Test Article',
      author: [{ family: 'Test', given: 'Author' }],
      URL: 'https://example.com/format-test',
      accessed: { 'date-parts': [[2024, 1, 15]] },
      issued: { 'date-parts': [[2024, 1, 10]] }
    }
  }];
  
  const formats = ['mla', 'apa'];
  const results = {};
  
  for (const format of formats) {
    try {
      console.log(`📤 测试${format.toUpperCase()}格式...`);
      
      const response = await fetch(`${API_BASE}/api/citations/format`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          citations: mockCitation,
          citation_format: format,
          html: true
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        results[format] = true;
        console.log(`✅ ${format.toUpperCase()}格式成功`);
      } else {
        results[format] = false;
        console.error(`❌ ${format.toUpperCase()}格式失败:`, response.status);
      }
      
    } catch (error) {
      results[format] = false;
      console.error(`❌ ${format.toUpperCase()}格式异常:`, error);
    }
  }
  
  return Object.values(results).every(result => result === true);
}

/**
 * 主测试函数
 */
async function runFormatTests() {
  console.log('🚀 开始引用格式化修复测试');
  console.log('测试目标: 验证format_only模式的参数验证修复');
  console.log('修复内容: Lambda函数不再要求title和content字段');
  console.log('='.repeat(60));
  
  const tests = [
    { name: '正常引用格式化', test: testCitationFormatting },
    { name: '空引用数组处理', test: testEmptyCitations },
    { name: '无效引用数据处理', test: testInvalidCitations },
    { name: '不同格式支持', test: testDifferentFormats }
  ];
  
  const results = {};
  
  for (const { name, test } of tests) {
    try {
      results[name] = await test();
    } catch (error) {
      console.error(`❌ ${name}测试异常:`, error);
      results[name] = false;
    }
  }
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总');
  console.log('='.repeat(30));
  
  for (const [testName, success] of Object.entries(results)) {
    const status = success ? '✅ 通过' : '❌ 失败';
    console.log(`${testName}: ${status}`);
  }
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('\n🎉 所有测试通过！引用格式化修复成功！');
  } else {
    console.log('\n⚠️  部分测试失败，需要进一步检查');
  }
  
  return results;
}

// 运行测试
runFormatTests().catch(console.error);
